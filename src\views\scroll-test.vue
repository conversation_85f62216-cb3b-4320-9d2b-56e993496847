<template>
    <div class="scroll-test-page">
        <div class="content-section" v-for="n in 20" :key="n">
            <div class="section-title">测试区域 {{ n }}</div>
            <div class="section-content">
                这是第 {{ n }} 个测试区域的内容。用于测试路由切换时的滚动行为。
                <br>
                当你从其他页面切换到这个页面时，应该自动滚动到页面顶部。
                <br>
                如果你在页面中间位置，然后切换到其他页面再回来，也应该回到顶部。
            </div>
        </div>
        
        <div class="navigation-buttons">
            <button @click="goToHome" class="nav-btn">回到首页</button>
            <button @click="goToProducts" class="nav-btn">产品页面</button>
            <button @click="goToAbout" class="nav-btn">关于我们</button>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToHome = () => {
    router.push('/')
}

const goToProducts = () => {
    router.push('/products')
}

const goToAbout = () => {
    router.push('/about')
}
</script>

<style lang="less" scoped>
.scroll-test-page {
    padding: 40px;
    
    .content-section {
        margin-bottom: 60px;
        padding: 40px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        
        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }
        
        .section-content {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
        }
    }
    
    .navigation-buttons {
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: flex;
        gap: 10px;
        
        .nav-btn {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
            
            &:hover {
                background: #0056b3;
            }
        }
    }
}
</style>
