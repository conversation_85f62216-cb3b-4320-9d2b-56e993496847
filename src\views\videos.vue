<template>
    <div class="videos-page">
        <!-- 顶部导航 -->
        <!-- <div class="top-nav">
            <div class="nav-container">
                <div class="logo">
                    <img src="https://picsum.photos/120/40?random=100" alt="EIR" />
                </div>
                <div class="nav-center">
                    <div class="nav-links">
                        <router-link to="/" class="nav-link">首页</router-link>
                        <router-link to="/about" class="nav-link">关于我们</router-link>
                        <router-link to="/products" class="nav-link">主要产品</router-link>
                        <router-link to="/videos" class="nav-link">视频演示</router-link>
                        <router-link to="/news" class="nav-link">新闻动态</router-link>
                        <router-link to="/tech" class="nav-link">技术支持</router-link>
                        <div class="nav-link language-selector">
                            EN
                            <i class="arrow-down"></i>
                        </div>
                    </div>
                </div>
                <div class="contact-btn">
                    联系我们
                </div>
            </div>
        </div> -->

        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">视频演示</h1>
        </div>

        <!-- 视频列表 -->
        <div class="videos-container">
            <div class="video-item" v-for="video in videosData" :key="video.id" @click="goToVideoDetail(video.id)">
                <div class="video-thumbnail">
                    <img :src="video.thumbnail" :alt="video.title" />
                    <div class="play-button">
                        <i class="play-icon"></i>
                    </div>
                </div>
                <div class="video-content">
                    <div class="video-description">
                        <p>{{ video.description }}</p>
                    </div>
                    <div class="video-actions">
                        <button class="action-btn">查看详情</button>
                        <button class="action-btn">了解更多</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination">
            <div class="pagination-arrow">
                <i class="arrow-right"></i>
            </div>
        </div>

        <!-- 底部联系信息 -->
        <!-- <div class="footer">
            <div class="footer-content">
                <div class="footer-left">
                    <h3>联系我们</h3>
                    <p>官方网站：www.eir.com.cn</p>
                    <p>咨询电话：400-xxx-xxxx</p>
                    <p>人才邮箱：<EMAIL></p>
                    <p>公司地址：四川省成都市高新区XXXXXXXXXXXXXXX</p>
                </div>
                <div class="footer-center">
                    <h3>订阅咨询</h3>
                    <p>Email: <EMAIL></p>
                    <p>(+86)28 2534 8639</p>
                    <p>(+86)755 2399 1467</p>
                </div>
                <div class="footer-right">
                    <div class="qr-code">
                        <img src="https://picsum.photos/100/100?random=1" alt="二维码" />
                        <p>联系我们</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2024 四川省成都市高新区 蜀ICP备XXXXXXXX号</p>
            </div>
        </div> -->
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 视频数据
const videosData = ref([
    {
        id: 1,
        title: '视频演示1',
        description: '我们的一体化关节、分层运控架构，情感驱动引擎已授权20多项专利，发表论文100余篇与电子科技大学、天府缘溪实验室合作，持续优化算法，建设数采工厂',
        thumbnail: 'https://picsum.photos/400/250?random=1'
    },
    {
        id: 2,
        title: '视频演示2',
        description: '我们的一体化关节、分层运控架构，情感驱动引擎已授权20多项专利，发表论文100余篇与电子科技大学、天府缘溪实验室合作，持续优化算法，建设数采工厂',
        thumbnail: 'https://picsum.photos/400/250?random=2'
    },
    {
        id: 3,
        title: '视频演示3',
        description: '我们的一体化关节、分层运控架构，情感驱动引擎已授权20多项专利，发表论文100余篇与电子科技大学、天府缘溪实验室合作，持续优化算法，建设数采工厂',
        thumbnail: 'https://picsum.photos/400/250?random=3'
    },
    {
        id: 4,
        title: '视频演示4',
        description: '我们的一体化关节、分层运控架构，情感驱动引擎已授权20多项专利，发表论文100余篇与电子科技大学、天府缘溪实验室合作，持续优化算法，建设数采工厂',
        thumbnail: 'https://picsum.photos/400/250?random=4'
    },
    {
        id: 5,
        title: '视频演示5',
        description: '我们的一体化关节、分层运控架构，情感驱动引擎已授权20多项专利，发表论文100余篇与电子科技大学、天府缘溪实验室合作，持续优化算法，建设数采工厂',
        thumbnail: 'https://picsum.photos/400/250?random=5'
    }
])

// 跳转到视频详情
const goToVideoDetail = (videoId) => {
    router.push(`/videos/${videoId}`)
}
</script>

<style lang="less" scoped>
.videos-page {
    min-height: 100vh;
    background: #f5f5f5;

    .top-nav {
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 0;
        z-index: 100;

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 70px;

            .logo {
                img {
                    height: 40px;
                }
            }

            .nav-center {
                flex: 1;
                display: flex;
                justify-content: center;

                .nav-links {
                    display: flex;
                    gap: 40px;
                    align-items: center;

                    .nav-link {
                        color: #666666;
                        text-decoration: none;
                        font-size: 20px;
                        font-weight: 400;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        transition: color 0.3s;

                        &:hover {
                            color: #555;
                        }

                        &.router-link-active {
                            font-weight: bold;
                            color: #1A1A1A;
                        }
                    }

                    .language-selector {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        cursor: pointer;

                        .arrow-down {
                            width: 0;
                            height: 0;
                            border-left: 4px solid transparent;
                            border-right: 4px solid transparent;
                            border-top: 4px solid #333;
                        }
                    }
                }
            }

            .contact-btn {
                background: #333;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.3s;

                &:hover {
                    background: #555;
                }
            }
        }
    }

    .page-header {
        text-align: center;
        padding: 60px 0;
        background: white;

        .page-title {
            font-size: 48px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
    }

    .videos-container {
        max-width: 1200px;
        margin: 60px auto;
        padding: 0 20px;

        .video-item {
            display: flex;
            margin-bottom: 60px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;

            &:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            }

            &:nth-child(even) {
                flex-direction: row-reverse;
            }

            .video-thumbnail {
                flex: 1;
                position: relative;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 300px;
                    object-fit: cover;
                    transition: transform 0.3s;
                }

                .play-button {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 60px;
                    height: 60px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: background 0.3s;

                    &:hover {
                        background: white;
                    }

                    .play-icon {
                        width: 0;
                        height: 0;
                        border-left: 20px solid #333;
                        border-top: 12px solid transparent;
                        border-bottom: 12px solid transparent;
                        margin-left: 4px;
                    }
                }

                &:hover img {
                    transform: scale(1.05);
                }
            }

            .video-content {
                flex: 1;
                padding: 40px;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .video-description {
                    margin-bottom: 30px;

                    p {
                        font-size: 16px;
                        line-height: 1.8;
                        color: #666;
                        margin: 0;
                    }
                }

                .video-actions {
                    display: flex;
                    gap: 20px;

                    .action-btn {
                        padding: 12px 24px;
                        border: 1px solid #ddd;
                        background: white;
                        color: #333;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: all 0.3s;
                        font-size: 14px;

                        &:hover {
                            background: #333;
                            color: white;
                            border-color: #333;
                        }
                    }
                }
            }
        }
    }

    .pagination {
        text-align: center;
        padding: 40px 0;

        .pagination-arrow {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                background: #333;
                border-color: #333;

                .arrow-right {
                    border-left-color: white;
                }
            }

            .arrow-right {
                width: 0;
                height: 0;
                border-left: 8px solid #333;
                border-top: 6px solid transparent;
                border-bottom: 6px solid transparent;
                transition: border-left-color 0.3s;
            }
        }
    }

    .footer {
        background: #333;
        color: white;
        padding: 60px 0 20px;

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;

            .footer-left, .footer-center {
                flex: 1;

                h3 {
                    font-size: 18px;
                    margin-bottom: 20px;
                    color: white;
                }

                p {
                    margin: 8px 0;
                    color: #ccc;
                    font-size: 14px;
                }
            }

            .footer-right {
                .qr-code {
                    text-align: center;

                    img {
                        width: 100px;
                        height: 100px;
                        margin-bottom: 10px;
                    }

                    p {
                        color: #ccc;
                        font-size: 14px;
                        margin: 0;
                    }
                }
            }
        }

        .footer-bottom {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #555;

            p {
                color: #999;
                font-size: 12px;
                margin: 0;
            }
        }
    }
}
</style>
