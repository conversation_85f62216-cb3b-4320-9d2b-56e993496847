import { router } from '@/router';
import axios from 'axios';
import { ElMessage, ElLoading } from 'element-plus';
// import { useUserInfoStore } from '@/store/user';

// 添加防抖函数
let lastErrorMessageTime = 0;
const showErrorMessage = (message) => {
  const now = Date.now();
  if (now - lastErrorMessageTime > 2000) { // 2秒内不重复显示
    ElMessage.error(message);
    lastErrorMessageTime = now;
  }
};

console.log(`
  /\\_/\\  
 ( o.o ) 
  > ^ <  
`);

// 创建axios实例
export const httpInstance = axios.create({
  timeout: 15000, // 15秒超时
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
  withCredentials: true,
});

// 请求拦截器
httpInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, (error) => {
  return Promise.reject(error);
});

// 设置基础URL
// if (process.env.NODE_ENV == 'development') {
httpInstance.defaults.baseURL = import.meta.env.VITE_BASE_URL_DEV;
httpInstance.defaults.url = import.meta.env.VITE_BASE_URL_DEV
// }
// 响应拦截器
export const $http = async (config) => {
  let loadingInstance;
  // 显示加载中
  if (config.url == '/user/loginByPassword') {
    loadingInstance = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    });
  }
  // console.log('请求头:', config);

  try {
    const axiosResponse = await httpInstance(config);
    const response = axiosResponse.data;
    // console.log('返回响应', axiosResponse);


    // 如果是登录 则保存token
    if (axiosResponse.config.url == '/user/loginByPassword') {
      const token = axiosResponse.headers.authorization.split(' ')[1]
      localStorage.setItem('token', token)
    }

    const res = {
      code: axiosResponse.status,
      data: response.data,
      message: response.message
    }
    console.log('返回结果', res);
    // 请求成功但业务逻辑失败
    if (res && res.code !== 200) {
      // 根据不同状态码处理
      let errorMessage = res.message || res.message || '未知错误';
      switch (res.code) {
        case 400:
          showErrorMessage(`请求错误: ${errorMessage}`);
          break;
        case 401:
          showErrorMessage(`未授权: ${errorMessage}`);
          // 清除token并跳转到登录页
          localStorage.removeItem('token');
          router.push('/login');
          break;
        case 403:
          showErrorMessage(`禁止访问: ${errorMessage}`);
          break;
        case 404:
          showErrorMessage(`资源不存在: ${errorMessage}`);
          break;
        case 500:
          showErrorMessage(`服务器错误: ${errorMessage}`);
          break;
        default:
          showErrorMessage(errorMessage);
      }
      console.log('返回结果', res);

      return res
    }
    return res

  } catch (err) {
    // // 网络请求错误处理
    console.log('错误', err);
    const errData = err.response
    if (err.isAxiosError) {
      const status = err.response?.status;
      const errorMsg = err.response?.data?.message || err.message || '网络错误';

      if (status === 401) {
        // 清除token并跳转到登录页
        localStorage.removeItem('token');
        router.push('/login');
        showErrorMessage('登录已过期，请重新登录');
      } else {
        // ElMessage.error(errorMsg);
      }
    } else {
      showErrorMessage(`请求异常: ${err.message}`);
    }

    return {
      code: errData.code,
      message: errData.data.message,
      data: null
    };
  } finally {
    // 关闭加载中
    loadingInstance?.close();
  }
}; 