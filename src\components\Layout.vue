<template>
    <div class="layout-container">
        <!-- 公共头部 -->
        <AppHeader />
        
        <!-- 主要内容区域 -->
        <main class="main-content">
            <router-view v-slot="{ Component }">
                <transition name="fade" mode="out-in">
                    <component :is="Component" />
                </transition>
            </router-view>
        </main>
        
        <!-- 公共页脚 -->
        <AppFooter />
    </div>
</template>

<script setup>
import { watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { forceScrollToTop } from '../utils/scrollManager'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'

const route = useRoute()

// 监听路由变化，滚动到顶部
watch(() => route.path, async () => {
    await nextTick()
    forceScrollToTop()
}, { flush: 'post' })
</script>

<style lang="scss" scoped>
.layout-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #fff;
    overflow-x: hidden; // 防止水平滚动条

    .main-content {
        flex: 1;
        margin-top: 96px;
        min-height: calc(100vh - 96px);
        display: flex;
        flex-direction: column;
    }
}

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
