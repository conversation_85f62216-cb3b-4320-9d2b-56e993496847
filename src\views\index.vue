<template>
    <div class="home-container">
        <section class="hero-banner">
            <el-carousel :interval="3000" :arrow="'never'" indicator-position="bottom" class="banner-carousel">
                <el-carousel-item v-for="(slide, index) in bannerSlides" :key="index">
                    <img class="banner-image" :src="slide.image" :alt="`轮播图${index + 1}`" />
                </el-carousel-item>
            </el-carousel>
        </section>
        <section class="hero-banner-1">
            <div class="banner-wrapper">
                <img src="/src/assets/imgs/<EMAIL>" alt="智能机器人产品" />
                <div class="overlay-content">
                    <div class="section-title">创造有温度的机器人</div>
                    <div class="section-subtitle">让机器人成为从人生的第一个朋友到最后一个家人</div>
                    <div class="action-buttons">
                        <button class="btn-primary" @click="goToPage('/products')">了解更多</button>
                        <button class="btn-secondary" @click="goToPage('/contact')">立即咨询</button>
                    </div>
                </div>
            </div>
        </section>
        <section class="hero-banner-2">
            <div class="banner-wrapper">
                <img src="/src/assets/imgs/<EMAIL>" alt="天行者SKYWALKER人形机器人" />
                <div class="overlay-content">
                    <div class="text-content">
                        <div class="section-title">天行者 SKYWALKER 人形机器人</div>
                        <div class="section-subtitle">三大核心技术</div>
                        <div class="feature-list">
                            <div class="flex_warp">
                                <div></div>
                                <div>自研高端发运算化关节技术</div>
                            </div>
                            <div class="flex_warp">
                                <div></div>
                                <div>分层控制架构</div>
                            </div>
                            <div class="flex_warp">
                                <div></div>
                                <div>情感驱动引擎</div>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="btn-primary" @click="goToPage('/products')">了解更多</button>
                            <button class="btn-secondary" @click="goToPage('/consult')">订购咨询</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="development-history">
            <div class="history-container">
                <div class="history-title">发展历程</div>
                <div class="timeline-wrapper">
                    <div class="timeline-arrow">
                        <img src="/src/assets/imgs/Frame.png" alt="向上箭头" />
                    </div>
                    <div class="timeline-container">
                        <div class="timeline-line"></div>
                        <div v-for="(item, index) in historySteps" :key="index" class="timeline-item">
                            <div class="timeline-date">{{ item.date }}</div>
                            <div class="timeline-node" v-if="index !== 0 && index !== historySteps.length - 1">
                                <div></div>
                            </div>
                            <div class="timeline-content"
                                :style="{ 'margin-left': index !== 0 && index !== historySteps.length - 1 ? '0px' : '20px' }">
                                <div class="content-title">{{ item.title }}</div>
                                <div v-if="item.description" class="content-desc">{{ item.description }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="timeline-arrow2">
                        <img src="/src/assets/imgs/<EMAIL>" alt="时钟图标" />
                    </div>
                </div>
            </div>
        </section>
        <section class="video-demo">
            <div class="video-demo-container">
                <div class="video-demo-title">视频演示</div>
                <div class="video-demo-subtitle">为科技人才入驻特有——从人工智能一个解决方案——个整人</div>
                <div class="video-carousel-wrapper">
                    <div class="carousel-container">
                        <el-carousel :interval="4000" type="card" indicator-position="none" arrow="never"
                            card-scale="0.75" class="video-carousel" @change="onCarouselChange" ref="videoCarousel">
                            <el-carousel-item v-for="(slide, index) in videoSlides" :key="index" class="carousel-item">
                                <div class="video-card" @click="handleCardClick(index)">
                                    <img :src="slide.poster" :alt="`视频演示${index + 1}`" class="video-poster" />
                                </div>
                            </el-carousel-item>
                        </el-carousel>
                    </div>
                </div>
            </div>
        </section>
        <section class="news-section">
            <div class="news-container">
                <div class="news-layout">
                    <div class="news-left">
                        <div class="news-title">新闻动态</div>
                        <div class="news-subtitle">全球关注人形机器人引领者</div>
                        <div class="more-btn" @click="viewMoreNews">
                            了解更多
                        </div>
                        <div class="news-pagination">
                            <img src="/src/assets/imgs/Vector@2x (1).png" alt="上一页" />
                            <div></div>
                            <img src="/src/assets/imgs/<EMAIL>" alt="下一页" />
                        </div>
                    </div>
                    <div class="news-right">
                        <div class="news-cards">
                            <div v-for="news in newsData" :key="news.id" class="news-card" @click="goToNewsDetail(news.id)">
                                <div class="news-image">
                                    <img :src="news.image" :alt="news.title" />
                                </div>
                                <div class="news-info">
                                    <div class="news-card-title">{{ news.title }}</div>
                                    <div class="news-card-content">{{ news.content }}</div>
                                    <div class="news-date">
                                        <div class="news-time">{{ news.date }}</div>
                                        <img src="/src/assets/imgs/Vector@2x (4).png" />
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const bannerSlides = ref([
    { image: '/src/assets/imgs/banner.png' },
    { image: '/src/assets/imgs/banner.png' },
    { image: '/src/assets/imgs/banner.png' },
    { image: '/src/assets/imgs/banner.png' },
    { image: '/src/assets/imgs/banner.png' }
])

const historySteps = ref([
    {
        date: '2025',
        title: '具身科技将发布更多款式平机器人产品，奔走关键四川省人形机器人训练场建设',
        description: ''
    },
    {
        date: '2025.06',
        title: '具身科技专家团队领衔成立全国人形机器人训练场地中心',
        description: '核心中心，团队成员万于工程手机机器人的基础核心力量，以零硬件基础等优先本体，具身科技初期专注于涉足全传感和外部传输化设施，大型定义配备影伸传道其起和涉外传输化。'
    },
    {
        date: '2025.05',
        title: '具身科技完成融资',
        description: '完成5个月，推出具身人形机器人科技有限公司完成数千万元天使轮融资，估值超过亿元，为川渝地区优质AI机器人产业。'
    },
    {
        date: '2025.04',
        title: '具身科技推动行业规范',
        description: '各界共同维护发展相关的ISO国际标准体系建设，促进新基础设施Q品质提升，为行业新基础设施发展贡献力量。'
    },
    {
        date: '2025.01',
        title: '天行者1号亮相电子科技大学',
        description: '细致与前沿齐，发布国内首款人形机器人，"新青理想"未来。'
    },
    {
        date: '2024.12',
        title: '天行者1号发布',
        description: '历时69天，具身科技发布自研的四川首台具身化人形机器人——天行者1号，其核心零部件本地化率高达90%。'
    },
    {
        date: '2024.09',
        title: '公司成立',
        description: '四川具身人形机器人科技有限公司成立'
    }
])

const newsData = ref([
    {
        id: 1,
        image: 'https://picsum.photos/400/250?random=1',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.05'
    },
    {
        id: 2,
        image: 'https://picsum.photos/400/250?random=2',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.04'
    },
    {
        id: 3,
        image: 'https://picsum.photos/400/250?random=3',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.03'
    }
])

const videoSlides = ref([
    { poster: '/src/assets/imgs/<EMAIL>' },
    { poster: '/src/assets/imgs/banner.png' },
    { poster: '/src/assets/imgs/banner.png' }
])

const currentVideoIndex = ref(0)
const videoCarousel = ref(null)

const goToPage = (path) => {
    router.push(path)
}

const onCarouselChange = (current) => {
    currentVideoIndex.value = current
}

const handleCardClick = (index) => {
    router.push('/videos')
}

const viewMoreNews = () => {
    router.push('/news')
}

const goToNewsDetail = (newsId) => {
    router.push('/news')
}

onMounted(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
})
</script>

<style lang="less" scoped>
.home-container {
    width: 100%;
    min-height: 100vh;
    background: #fff;
    overflow-x: hidden; // 防止水平滚动条





    // 主横幅轮播
    .hero-banner {
        margin-left: calc(-50vw + 50%);
        height: 984px;

        .banner-carousel {
            position: relative;
            width: 100%;
            height: 100%;

            :deep(.el-carousel__container) {
                width: 100%;
                height: 100%;
            }

            :deep(.el-carousel__item) {
                display: flex;
                align-items: center;
                justify-content: center;

                .banner-image {
                    width: 100%;
                    height: 100%;

                }
            }

            // 自定义指示器样式
            :deep(.el-carousel__indicators) {
                position: absolute;
                bottom: 6px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 10;

                .el-carousel__indicator {
                    margin: 0 8px;

                    .el-carousel__button {
                        width: 40px;
                        height: 5px;
                        background: #A7A7A7;
                        opacity: 1;

                        &:hover {
                            background: #000;
                        }
                    }

                    &.is-active .el-carousel__button {
                        background: #000;
                        width: 40px;
                    }
                }
            }


        }
    }

    .hero-banner-1 {
        width: 100%;
        padding: 0 27px;
        margin-top: 27px;
        user-select: none;

        .banner-wrapper {
            position: relative;
            width: 100%;
            height: 100%;

            img {
                width: 100%;
                height: 100%;
                display: block;
            }

            .overlay-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: white;
                z-index: 10;

                .section-title {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: bold;
                    font-size: 64px;
                    color: #1A1A1A;
                    margin-bottom: 64px;
                }

                .section-subtitle {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    font-size: 37px;
                    color: #4D4D4D;
                    margin-bottom: 93px;

                }

                .action-buttons {
                    display: flex;
                    gap: 32px;
                    justify-content: center;

                    .btn-primary {
                        width: 133px;
                        height: 43px;
                        border-radius: 24px;
                        border: 1px solid #333333;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 300;
                        font-size: 19px;
                        color: #333333;
                        text-align: center;
                        line-height: 20px;
                        cursor: pointer;

                        &:hover {

                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                        }
                    }

                    .btn-secondary {
                        width: 133px;
                        height: 43px;
                        border-radius: 24px;
                        border: 1px solid #333333;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 300;
                        font-size: 19px;
                        color: #333333;
                        text-align: center;
                        line-height: 20px;

                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                        }
                    }
                }
            }
        }


    }

    .hero-banner-2 {
        width: 100%;
        padding: 0 27px;
        margin-top: 27px;
        user-select: none;

        .banner-wrapper {
            position: relative;
            width: 100%;
            height: 100%;

            img {
                width: 100%;
                height: 100%;
                display: block;
            }

            .overlay-content {
                position: absolute;
                top: 50%;
                left: 224px;
                transform: translateY(-50%);
                z-index: 10;

                .text-content {
                    text-align: left;

                    .section-title {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: bold;
                        font-size: 48px;
                        color: #1A1A1A;
                        margin-bottom: 75px;
                    }

                    .section-subtitle {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: bold;
                        font-size: 32px;
                        color: #333333;
                        margin-bottom: 10px;

                    }

                    .feature-list {
                        list-style: none;
                        padding: 0;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 24px;
                        color: #666666;
                        line-height: 1.8;
                        margin-bottom: 88px;

                        .flex_warp {
                            display: flex;
                            align-items: center;

                            div:first-child {
                                width: 5px;
                                height: 5px;
                                background: #666666;
                                border-radius: 50%;
                            }
                        }
                    }

                    .action-buttons {
                        display: flex;
                        gap: 32px;
                        justify-content: flex-start;

                        .btn-primary {
                            width: 133px;
                            height: 43px;
                            border-radius: 24px;
                            border: 1px solid #333333;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 300;
                            font-size: 19px;
                            color: #333333;
                            text-align: center;
                            line-height: 20px;
                            cursor: pointer;

                            &:hover {

                                transform: translateY(-2px);
                                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                            }
                        }

                        .btn-secondary {
                            width: 133px;
                            height: 43px;
                            border-radius: 24px;
                            border: 1px solid #333333;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 300;
                            font-size: 19px;
                            color: #333333;
                            text-align: center;
                            line-height: 20px;

                            &:hover {
                                transform: translateY(-2px);
                                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                            }
                        }
                    }
                }
            }
        }


    }



    // 发展历程模块
    .development-history {
        width: 100%;
        padding: 0 237px;
        margin-top: 133px;
        // background: #F8F8F8;
        // padding-bottom: 80px;

        .history-container {
            margin: 0 auto;

            .history-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 50px;
                color: #1A1A1A;
                text-align: left;
                margin-bottom: 80px;
            }

            .timeline-wrapper {
                position: relative;
                margin-top: 40px;

                .timeline-arrow {
                    position: absolute;
                    left: 241px;
                    top: -33px;
                    z-index: 3;
                    transform: translateX(-50%);

                    img {
                        width: 33px;
                        height: 33px;
                        object-fit: contain;
                    }
                }

                .timeline-arrow2 {
                    position: absolute;
                    left: 241px;
                    bottom: 14.5px;
                    z-index: 3;
                    transform: translateX(-50%);

                    img {
                        width: 33px;
                        height: 33px;
                        object-fit: contain;
                    }
                }

                .timeline-container {
                    position: relative;
                    padding-left: 20px;

                    .timeline-line {
                        position: absolute;
                        left: 240px;
                        top: 0;
                        bottom: 0;
                        width: 1px;
                        background: #333;
                        z-index: 1;
                        height: 94.7%;

                    }

                    .timeline-item {
                        position: relative;
                        display: flex;
                        align-items: flex-start;
                        margin-bottom: 65px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .timeline-date {
                            width: 121px;
                            height: 39px;
                            font-weight: bolder;
                            font-size: 28px;
                            color: #1A1A1A;
                            margin-right: 203px;
                            flex-shrink: 0;
                            text-align: center;
                            margin-top: 14px;
                        }

                        .timeline-node {
                            width: 28px;
                            height: 28px;
                            border: 1px solid #333333;
                            position: relative;
                            border-radius: 50%;
                            background: #fff;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            top: 16.5px;
                            right: 117px;
                            z-index: 10;
                            position: relative;

                            div {
                                width: 11px;
                                height: 11px;
                                background: #333333;
                                border-radius: 50%;
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                            }
                        }

                        .timeline-content {
                            flex: 1;

                            .content-title {
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 500;
                                font-size: 23px;
                                color: #1A1A1A;
                                // line-height: 1.4;
                                margin-bottom: 18px;
                            }

                            .content-desc {
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-size: 17px;
                                color: #4D4D4D;
                                // line-height: 1.4;
                            }
                        }
                    }
                }
            }


        }


    }

    // 视频演示模块
    .video-demo {
        width: 100%;
        // padding: 133px 237px 80px;
        // background: #F8F8F8;
        padding-top: 113px;
        padding-bottom: 135px;
        user-select: none;

        .video-demo-container {
            margin: 0 auto;

            .video-demo-title {
                color: #1A1A1A;
                margin-bottom: 27px;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 53px;
                color: #1A1A1A;
                text-align: center;
            }

            .video-demo-subtitle {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 24px;
                color: #4D4D4D;
                text-align: center;
                margin-bottom: 53px;
            }

            .video-carousel-wrapper {
                position: relative;
                width: 150%;
                display: flex;
                justify-content: center;
                padding: 0;
                overflow: hidden;
                left: -25%;

                .carousel-container {
                    width: 100%;
                    margin: 0 auto;
                    position: relative;

                    .video-carousel {
                        width: 100%;
                        overflow: hidden;

                        :deep(.el-carousel__container) {
                            // perspective: 1200px;
                            height: 825px;
                        }

                        // 隐藏指示器
                        :deep(.el-carousel__indicators) {
                            display: none !important;
                        }

                        :deep(.el-carousel__item) {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.5s ease;

                            &.is-active {
                                transform: scale(1);
                                z-index: 10;
                                opacity: 1;

                                .video-card {
                                    width: 1467px;
                                    height: 825px;
                                    // border-radius: 20px;
                                    overflow: hidden;
                                    position: relative;

                                    .video-poster {
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                        display: block;
                                    }
                                }
                            }

                            &:not(.is-active) {
                                transform: scale(0.75);
                                opacity: 0.7;
                                cursor: pointer;


                                .video-card {
                                    width: 250px;
                                    height: 773px;
                                    transition: all 0.3s ease;
                                    // border-radius: 20px;
                                    overflow: hidden;
                                    position: relative;

                                    &:hover {
                                        transform: translateY(-5px);
                                        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
                                    }

                                    .video-poster {
                                        width: 1375px;
                                        height: 100%;
                                        object-fit: cover;
                                        display: block;
                                        position: absolute;
                                        // left: -1125px;
                                    }
                                }

                                // &:last-child {
                                //     .video-card {
                                //         .video-poster {
                                //             // left: 0 !important;
                                //         }
                                // }
                                // }

                            }

                        }
                    }





                }
            }
        }
    }

    // 新闻动态样式
    .news-section {
        padding: 133px 160px;
        background: #F4F6F7;

        .news-container {
            // max-width: 1200px;
            margin: 0 auto;

            .news-layout {
                display: flex;
                gap: 107px;
                align-items: center;

                // 左侧标题区域
                .news-left {
                    // flex: 0 0 300px;

                    .news-title {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        font-size: 53px;
                        color: #1A1A1A;
                        margin-bottom: 27px;
                    }

                    .news-subtitle {
                        width: 288px;
                        font-weight: 400;
                        font-size: 24px;
                        color: #4D4D4D;
                        margin-bottom: 73px;
                    }

                    .more-btn {
                        width: 133px;
                        height: 43px;
                        border-radius: 24px;
                        border: 1px solid #333333;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 300;
                        font-size: 19px;
                        color: #333333;
                        text-align: center;
                        line-height: 38px;
                        cursor: pointer;
                        margin-bottom: 189px;

                        &:hover {

                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                        }
                    }

                    // 分页控制
                    .news-pagination {
                        display: flex;
                        align-items: center;
                        gap: 57px;

                        div {
                            width: 1px;
                            height: 53px;
                            background: #D9D9D9;
                        }

                        img {
                            width: 67px;
                            height: 16px;
                            cursor: pointer;
                        }
                    }
                }

                // 右侧新闻卡片区域
                .news-right {
                    flex: 1;

                    .news-cards {
                        display: flex;
                        align-items: center;
                        gap: 43px;

                        .news-card {
                            // background: #fff;
                            // border-radius: 8px;
                            // overflow: hidden;
                            // transition: all 0.3s ease;
                            cursor: pointer;

                            // &:hover {
                            //     transform: translateY(-4px);
                            //     box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
                            // }

                            .news-image {
                                width: 373px;
                                height: 280px;
                                overflow: hidden;

                                img {
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                    transition: transform 0.3s ease;
                                }

                                &:hover img {
                                    transform: scale(1.05);
                                }
                            }

                            .news-info {
                                // padding: 20px;

                                .news-card-title {
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 500;
                                    font-size: 24px;
                                    color: #1A1A1A;
                                    margin-bottom: 16px;
                                    margin-top: 27px;
                                    line-height: 1.4;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    display: -webkit-box;
                                    -webkit-line-clamp: 2;
                                    -webkit-box-orient: vertical;
                                    overflow: hidden;
                                }

                                .news-card-content {
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    font-size: 16px;
                                    color: #666666;
                                    line-height: 1.5;
                                    display: -webkit-box;
                                    -webkit-line-clamp: 2;
                                    -webkit-box-orient: vertical;
                                    overflow: hidden;
                                    margin-bottom: 43px;
                                }

                                .news-date {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;

                                    .news-time {
                                        font-family: Source Han Sans CN, Source Han Sans CN;
                                        font-weight: 400;
                                        font-size: 16px;
                                        color: #999999;
                                    }

                                    img {
                                        width: 32px;
                                        height: 8px;
                                        margin-right: 100px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 页脚样式
    .footer-section {
        background: #1A1A1A;
        color: #fff;
        // padding: 80px 160px;

        .footer-container {
            // max-width: 1200px;
            margin: 0 auto;
            // padding: 0 160px;

            .footer-content {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                // padding-bottom: 40px;
                border-bottom: 1px solid #595959;
                padding: 80px 160px;

                .footer-title {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    font-size: 32px;
                    color: #FFFFFF;
                    margin-bottom: 21px;
                }

                // 联系我们
                .footer-contact {
                    flex: 1;

                    .contact-item {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 19px;
                        color: #898989;
                        margin-bottom: 11px;

                        .contact-label {
                            color: #898989;
                        }

                        .contact-value {
                            color: #898989;
                        }
                    }
                }

                // 订阅咨询
                .footer-subscribe {
                    flex: 1;

                    .contact-item {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 19px;
                        color: #898989;
                        margin-bottom: 11px;

                        .contact-label {
                            color: #898989;
                        }

                        .contact-value {
                            color: #898989;
                        }
                    }
                }

                // 二维码
                .footer-qrcode {
                    flex: 0 0 auto;
                    text-align: center;

                    .qrcode-image {
                        width: 160px;
                        height: 160px;
                        margin-bottom: 12px;
                        border-radius: 8px;
                        overflow: hidden;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .qrcode-text {
                        font-size: 14px;
                        color: #898989;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                    }
                }
            }

            // 版权信息
            .footer-copyright {
                padding: 24px 0;
                text-align: center;

                .copyright-text {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 300;
                    font-size: 19px;
                    color: #898989;
                }
            }
        }
    }
}
</style>
