<template>
    <div class="products-container">
        <section class="hero-section">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">天行者1号 SKYWALKER II</h1>
                    <p class="hero-subtitle">具身智能人形机器人</p>
                    <button class="hero-btn" @click="scrollToSection('intro')">了解更多</button>
                </div>
                <div class="hero-image">
                    <img src="https://picsum.photos/600/800?random=1" alt="天行者1号机器人" />
                </div>
            </div>
        </section>

        <section class="intro-section" id="intro">
            <div class="intro-container">
                <h2 class="section-title">产品介绍</h2>
                <div class="intro-content">
                    <p class="intro-text">
                        天行者1号 SKYWALKER II 是具身科技自主研发的新一代人形机器人，集成了先进的人工智能技术、精密的机械结构和智能感知系统。
                        该机器人具备优异的运动能力、智能交互能力和环境适应能力，可广泛应用于工业制造、服务业、教育科研等多个领域。
                    </p>
                    <p class="intro-text">
                        通过创新的技术架构和精密的工程设计，天行者1号实现了人形机器人在复杂环境下的稳定运行，为未来智能制造和人机协作奠定了坚实基础。
                    </p>
                </div>
            </div>
        </section>

        <section class="robot-diagram-section">
            <div class="diagram-container">
                <div class="robot-image-wrapper">
                    <img src="https://picsum.photos/500/700?random=2" alt="机器人结构图" class="robot-image" />
                    <div class="annotation annotation-1">
                        <div class="dot"></div>
                        <div class="label">头部</div>
                        <div class="description">集成视觉系统</div>
                    </div>
                    <div class="annotation annotation-2">
                        <div class="dot"></div>
                        <div class="label">胸部</div>
                        <div class="description">核心控制单元</div>
                    </div>
                    <div class="annotation annotation-3">
                        <div class="dot"></div>
                        <div class="label">手臂</div>
                        <div class="description">精密操作系统</div>
                    </div>
                    <div class="annotation annotation-4">
                        <div class="dot"></div>
                        <div class="label">腿部</div>
                        <div class="description">动力驱动系统</div>
                    </div>
                    <div class="annotation annotation-5">
                        <div class="dot"></div>
                        <div class="label">关节</div>
                        <div class="description">智能关节技术</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="manufacturing-section">
            <div class="manufacturing-container">
                <div class="manufacturing-content">
                    <div class="text-content">
                        <h2 class="section-title">智能制造，精益自动化</h2>
                        <div class="feature-list">
                            <div class="feature-item">
                                <div class="feature-icon">1</div>
                                <div class="feature-text">高精度机械臂操作系统</div>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">2</div>
                                <div class="feature-text">智能视觉识别与定位</div>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">3</div>
                                <div class="feature-text">自适应环境感知能力</div>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">4</div>
                                <div class="feature-text">多任务协同作业模式</div>
                            </div>
                        </div>
                    </div>
                    <div class="image-content">
                        <img src="https://picsum.photos/600/400?random=3" alt="智能制造场景" />
                    </div>
                </div>
            </div>
        </section>

        <section class="motion-section">
            <div class="motion-container">
                <div class="motion-image">
                    <img src="https://picsum.photos/500/600?random=4" alt="运动能力展示" />
                </div>
                <div class="motion-content">
                    <h2 class="section-title">卓越运动能力</h2>
                    <p class="motion-description">
                        天行者1号具备出色的运动控制能力，能够在复杂环境中保持稳定的步态和精确的操作动作。
                        通过先进的平衡控制算法和动力学建模，实现了人形机器人的自然流畅运动。
                    </p>
                </div>
            </div>
        </section>

        <section class="interaction-section">
            <div class="interaction-container">
                <div class="interaction-content">
                    <h2 class="section-title">多模态感知交互体验</h2>
                    <p class="interaction-description">
                        集成多种传感器和交互模式，支持语音识别、手势识别、表情识别等多种人机交互方式，
                        为用户提供自然、直观的交互体验。
                    </p>
                </div>
                <div class="interaction-image">
                    <img src="https://picsum.photos/500/600?random=5" alt="交互体验" />
                </div>
            </div>
        </section>

        <section class="parameters-section">
            <div class="parameters-container">
                <h2 class="section-title">产品参数</h2>
                <div class="parameters-table">
                    <div class="table-header">
                        <div class="header-cell">参数项目</div>
                        <div class="header-cell">规格</div>
                        <div class="header-cell">单位</div>
                        <div class="header-cell">备注</div>
                    </div>
                    <div class="table-row" v-for="param in parameters" :key="param.name">
                        <div class="table-cell">{{ param.name }}</div>
                        <div class="table-cell">{{ param.value }}</div>
                        <div class="table-cell">{{ param.unit }}</div>
                        <div class="table-cell">{{ param.note }}</div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const parameters = ref([
    { name: '身高', value: '170', unit: 'cm', note: '标准配置' },
    { name: '体重', value: '65', unit: 'kg', note: '含电池' },
    { name: '自由度', value: '40', unit: 'DOF', note: '全身关节' },
    { name: '续航时间', value: '8', unit: '小时', note: '标准工作模式' },
    { name: '最大负载', value: '20', unit: 'kg', note: '双臂协作' },
    { name: '行走速度', value: '1.2', unit: 'm/s', note: '最大速度' },
    { name: '工作温度', value: '-10~50', unit: '℃', note: '环境适应' },
    { name: '防护等级', value: 'IP54', unit: '-', note: '防尘防水' }
])

const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
    }
}

onMounted(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
})
</script>

<style lang="less" scoped>
.products-container {
    width: 100%;
    min-height: 100vh;
    background: #fff;

    .hero-section {
        height: 100vh;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        position: relative;

        .hero-content {
            display: flex;
            align-items: center;
            max-width: 1200px;
            width: 100%;
            padding: 0 40px;
            gap: 80px;

            .hero-text {
                flex: 1;

                .hero-title {
                    font-size: 64px;
                    font-weight: bold;
                    margin-bottom: 24px;
                    line-height: 1.2;
                }

                .hero-subtitle {
                    font-size: 24px;
                    margin-bottom: 40px;
                    opacity: 0.9;
                }

                .hero-btn {
                    padding: 16px 32px;
                    background: rgba(255, 255, 255, 0.2);
                    border: 2px solid white;
                    color: white;
                    border-radius: 30px;
                    font-size: 18px;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:hover {
                        background: white;
                        color: #667eea;
                        transform: translateY(-2px);
                    }
                }
            }

            .hero-image {
                flex: 1;
                text-align: center;

                img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                }
            }
        }
    }

    .intro-section {
        padding: 120px 40px;
        background: #f8f9fa;

        .intro-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;

            .section-title {
                font-size: 48px;
                font-weight: bold;
                color: #1a1a1a;
                margin-bottom: 60px;
            }

            .intro-content {
                .intro-text {
                    font-size: 18px;
                    line-height: 1.8;
                    color: #666;
                    margin-bottom: 30px;
                    max-width: 800px;
                    margin-left: auto;
                    margin-right: auto;
                }
            }
        }
    }

    .robot-diagram-section {
        padding: 120px 40px;
        background: #1a1a1a;
        color: white;

        .diagram-container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;

            .robot-image-wrapper {
                position: relative;
                display: flex;
                justify-content: center;

                .robot-image {
                    max-width: 500px;
                    height: auto;
                }

                .annotation {
                    position: absolute;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 4px;

                    .dot {
                        width: 8px;
                        height: 8px;
                        background: #00ff88;
                        border-radius: 50%;
                        box-shadow: 0 0 15px #00ff88;
                        position: relative;

                        &::before {
                            content: '';
                            position: absolute;
                            width: 2px;
                            height: 30px;
                            background: #00ff88;
                            left: 50%;
                            top: 100%;
                            transform: translateX(-50%);
                        }
                    }

                    .label {
                        font-weight: bold;
                        font-size: 14px;
                        color: #00ff88;
                        margin-top: 35px;
                    }

                    .description {
                        font-size: 12px;
                        opacity: 0.7;
                        white-space: nowrap;
                    }

                    &.annotation-1 {
                        top: 15%;
                        left: 20%;
                    }

                    &.annotation-2 {
                        top: 35%;
                        right: 15%;
                    }

                    &.annotation-3 {
                        top: 45%;
                        left: 10%;
                    }

                    &.annotation-4 {
                        bottom: 25%;
                        right: 20%;
                    }

                    &.annotation-5 {
                        bottom: 15%;
                        left: 25%;
                    }
                }
            }
        }
    }

    .manufacturing-section {
        padding: 120px 40px;
        background: #2c3e50;
        color: white;

        .manufacturing-container {
            max-width: 1200px;
            margin: 0 auto;

            .manufacturing-content {
                display: flex;
                align-items: center;
                gap: 80px;

                .text-content {
                    flex: 1;

                    .section-title {
                        font-size: 48px;
                        font-weight: bold;
                        color: white;
                        margin-bottom: 40px;
                    }

                    .feature-list {
                        .feature-item {
                            display: flex;
                            align-items: center;
                            gap: 20px;
                            margin-bottom: 24px;

                            .feature-icon {
                                width: 40px;
                                height: 40px;
                                background: #00ff88;
                                color: #2c3e50;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-weight: bold;
                                font-size: 18px;
                            }

                            .feature-text {
                                font-size: 18px;
                                color: white;
                            }
                        }
                    }
                }

                .image-content {
                    flex: 1;

                    img {
                        width: 100%;
                        height: auto;
                        border-radius: 20px;
                        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    }
                }
            }
        }
    }

    .motion-section {
        padding: 120px 40px;
        background: #1a1a1a;
        color: white;

        .motion-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 80px;

            .motion-image {
                flex: 1;

                img {
                    width: 100%;
                    height: auto;
                    border-radius: 20px;
                }
            }

            .motion-content {
                flex: 1;

                .section-title {
                    font-size: 48px;
                    font-weight: bold;
                    margin-bottom: 30px;
                }

                .motion-description {
                    font-size: 18px;
                    line-height: 1.8;
                    opacity: 0.9;
                }
            }
        }
    }

    .interaction-section {
        padding: 120px 40px;
        background: #f8f9fa;

        .interaction-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 80px;

            .interaction-content {
                flex: 1;

                .section-title {
                    font-size: 48px;
                    font-weight: bold;
                    color: #1a1a1a;
                    margin-bottom: 30px;
                }

                .interaction-description {
                    font-size: 18px;
                    line-height: 1.8;
                    color: #666;
                }
            }

            .interaction-image {
                flex: 1;

                img {
                    width: 100%;
                    height: auto;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }

    .parameters-section {
        padding: 120px 40px;
        background: #1a1a1a;
        color: white;

        .parameters-container {
            max-width: 1200px;
            margin: 0 auto;

            .section-title {
                font-size: 48px;
                font-weight: bold;
                text-align: center;
                margin-bottom: 60px;
            }

            .parameters-table {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                overflow: hidden;

                .table-header {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                    background: rgba(255, 255, 255, 0.1);

                    .header-cell {
                        padding: 20px;
                        font-weight: bold;
                        font-size: 18px;
                        text-align: center;
                        border-right: 1px solid rgba(255, 255, 255, 0.1);

                        &:last-child {
                            border-right: none;
                        }
                    }
                }

                .table-row {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

                    &:last-child {
                        border-bottom: none;
                    }

                    .table-cell {
                        padding: 16px 20px;
                        text-align: center;
                        border-right: 1px solid rgba(255, 255, 255, 0.1);

                        &:last-child {
                            border-right: none;
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 768px) {
        .hero-section {
            .hero-content {
                flex-direction: column;
                gap: 40px;
                padding: 0 20px;

                .hero-text {
                    text-align: center;

                    .hero-title {
                        font-size: 36px;
                    }

                    .hero-subtitle {
                        font-size: 18px;
                    }
                }
            }
        }

        .manufacturing-section,
        .motion-section,
        .interaction-section {
            .manufacturing-container,
            .motion-container,
            .interaction-container {
                .manufacturing-content,
                .motion-container,
                .interaction-container {
                    flex-direction: column;
                    gap: 40px;
                }
            }
        }

        .robot-diagram-section {
            .diagram-container {
                .robot-image-wrapper {
                    .annotation {
                        position: static;
                        margin: 10px 0;
                        flex-direction: row;
                        align-items: center;

                        .dot::before {
                            display: none;
                        }

                        .label {
                            margin-top: 0;
                        }
                    }
                }
            }
        }

        .parameters-section {
            .parameters-container {
                .parameters-table {
                    .table-header,
                    .table-row {
                        grid-template-columns: 1fr;
                        text-align: left;

                        .header-cell,
                        .table-cell {
                            border-right: none;
                            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                        }
                    }
                }
            }
        }
    }
}
</style>