import { $http } from '.'

/**
 * 用户密码登录
 * @param {Object} data - 登录信息，包含username和password
 * @returns {Promise} API响应
 */
export const loginByPassword = async (data) => {
    return await $http({
        url: '/user/loginByPassword',
        method: 'post',
        data
    })
}

/**
 * 公告栏列表
 */
export const infoNoticeList = async (data) => {
    return await $http({
        url: '/dataview/notice/list',
        method: 'post',
        data
    })
}
/**
 * 主页领补模块
 */
export const userStatList = async (data) => {
    return await $http({
        url: '/dataView/userStat',
        method: 'post',
        data
    })
}


/**
 * 工作动态
 */
export const workDynamicLists = async (data) => {
    return await $http({
        url: '/dataview/workDynamic/list',
        method: 'post',
        data
    })
}

/**
 * 全市资金绩效
 */
export const infoAllFundsStat = async (data) => {
    return await $http({
        url: '/dataView/allFundsStat',
        method: 'post',
        data
    })
}
/**
 * 人员信息
 */
export const infoStayUserList = async (data) => {
    return await $http({
        url: '/dataView/stayUserList',
        method: 'post',
        data
    })
}

/**
 * 乡镇视频列表
 */
export const infoFilterTownList = async (data) => {
    return await $http({
        url: 'dataView/getFilterTownList',
        method: 'post',
        data
    })
}
/**
 * 领补列表
 */
export const countyClockList = async (data) => {
    return await $http({
        url: 'dataView/countyClockList',
        method: 'post',
        data
    })
}
/**
 * 领补列表展开
 */
export const townClockUser = async (data) => {
    return await $http({
        url: 'dataView/townClockUser',
        method: 'post',
        data
    })
}
/**
 * 请假列表
 */
export const leaveUserList = async (data) => {
    return await $http({
        url: 'dataView/leaveUserList',
        method: 'post',
        data
    })
}
/**
 * 删除请假
 */
export const leaveDelete = async (data) => {
    return await $http({
        url: 'dataView/leaveDelete',
        method: 'post',
        data
    })
}
/**
 * 报备列表
 */
export const countyRecordList = async (data) => {
    return await $http({
        url: 'dataView/countyRecordList',
        method: 'post',
        data
    })
}

/**
 * 删除报备
 */
export const recordDelete = async (data) => {
    return await $http({
        url: 'dataView/recordDelete',
        method: 'post',
        data
    })
}

/**
 * 车辆管理查询
 */
export const useList = async (data) => {
    return await $http({
        url: 'dataView/useList',
        method: 'post',
        data
    })
}
/**
 * 获取所有乡
 */
export const getFilterTownList = async (data) => {
    return await $http({
        url: 'dataView/getFilterTownList',
        method: 'post',
        data
    })
}
/**
 * 获取所有村
 */
export const getFilterTownVillageList = async (data) => {
    return await $http({
        url: 'dataView/getFilterTownVillageList',
        method: 'post',
        data
    })
}
/**
 * 车辆管理查询
 */
export const carListinfo = async (data) => {
    return await $http({
        url: 'dataView/carList',
        method: 'post',
        data
    })
}

/**
 * 诉求列表
 */
export const appealList = async (data) => {
    return await $http({
        url: 'dataView/appealList',
        method: 'post',
        data
    })
}
/**
 * 关心关爱列表
 */
export const carePolicyList = async (data) => {
    return await $http({
        url: 'dataView/carePolicyList',
        method: 'post',
        data
    })
}
/**
 * 删除关心关爱
 */

export const carePolicyDelete = async (data) => {
    return await $http({
        url: 'dataView/carePolicyDelete',
        method: 'post',
        data
    })
}
/**
 * 帮扶列表
 */

export const helplist = async (data) => {
    return await $http({
        url: 'dataview/help/list',
        method: 'post',
        data
    })
}

/**
 * 删除帮扶列表
 */
export const helpdelete = async (data) => {
    return await $http({
        url: 'dataview/help/delete',
        method: 'post',
        data
    })
}
/**
 * 工作动态
 */

export const workDynamiclist = async (data) => {
    return await $http({
        url: 'dataview/workDynamic/list',
        method: 'post',
        data
    })
}

/**
 * 删除工作动态
 */

export const workDynamideletet = async (data) => {
    return await $http({
        url: 'dataView/workDynamic/delete',
        method: 'post',
        data
    })
}


/**
 * 取消置顶
 */

export const workDynamsetTop = async (data) => {
    return await $http({
        url: 'dataview/workDynamic/setTop',
        method: 'post',
        data
    })
}

/**
 * 工作计划
 */

export const workPlanList = async (data) => {
    return await $http({
        url: 'dataView/workPlanList',
        method: 'post',
        data
    })
}


/**
 * 工作计划删除
 */

export const workPlanDelete = async (data) => {
    return await $http({
        url: 'dataView/workPlanDelete',
        method: 'post',
        data
    })
}

/**
 * 资料库列表
 */

export const resourcelist = async (data) => {
    return await $http({
        url: 'dataView/resource/list',
        method: 'post',
        data
    })
}

/**
 * .资料库删除
 */
export const resourceDelete = async (data) => {
    return await $http({
        url: 'dataView/resource/delete',
        method: 'post',
        data
    })
}

/**
 * 资料库列表
 */

export const noticelist = async (data) => {
    return await $http({
        url: 'dataView/notice/list',
        method: 'post',
        data
    })
}

/**
 * .资料库删除
 */
export const noticeDelete = async (data) => {
    return await $http({
        url: 'dataView/notice/delete',
        method: 'post',
        data
    })
}

/**
 * .职务
 */
export const getFilterUserRoleListApi = async (data) => {
    return await $http({
        url: 'dataView/getFilterUserRoleList',
        method: 'post',
        data
    })
}

/**
 * .资金绩效市级
 */
export const fundsStat = async (data) => {
    return await $http({
        url: 'dataView/fundsStat',
        method: 'post',
        data
    })
}

/**
 * .资金绩效乡镇
 */
export const fundsTownStat = async (data) => {
    return await $http({
        url: 'dataView/fundsTownStat',
        method: 'post',
        data
    })
}

/**
 * .资金绩效村
 */
export const fundsVillageStat = async (data) => {
    return await $http({
        url: 'dataView/fundsVillageStat',
        method: 'post',
        data
    })
}

/**
 * .资金绩效表格
 */
export const fundsList = async (data) => {
    return await $http({
        url: 'dataView/fundsList',
        method: 'post',
        data
    })
}


/**
 * 删除诉求
 */
export const appealDelete = async (data) => {
    return await $http({
        url: 'dataView/appealDelete',
        method: 'post',
        data
    })
}

/**
 * 驻村补助
 */
export const subsidyTownSentLog = async (data) => {
    return await $http({
        url: 'dataView/subsidyTownSentLog',
        method: 'post',
        data
    })
}
/**
 * 获取会议
 */
export const getMeetingList = async (data) => {
    return await $http({
        url: 'dataView/getMeetingList',
        method: 'post',
        data
    })
}

/**
 * 创建会议
 */
export const createMeeting = async (data) => {
    return await $http({
        url: 'dataView/createMeeting',
        method: 'post',
        data
    })
}
/**
 * 删除会议
 */
export const deleteMeeting = async (data) => {
    return await $http({
        url: 'dataView/deleteMeeting',
        method: 'post',
        data
    })
}

/**
 * 加入会议
 */
export const joinMeeting = async (data) => {
    return await $http({
        url: 'dataView/joinMeeting',
        method: 'post',
        data
    })
}

/**
 * 添加车辆
 */
export const carAdd = async (data) => {
    return await $http({
        url: 'dataView/carAdd',
        method: 'post',
        data
    })
}

/**
 * 获取所有驾驶员
 */
export const allDriver = async (data) => {
    return await $http({
        url: 'dataView/allDriver',
        method: 'post',
        data
    })
}


/**
 * 编辑车辆
 */
export const carEdit = async (data) => {
    return await $http({
        url: 'dataView/carEdit',
        method: 'post',
        data
    })
}

/**
 * 资料库详情
 */
export const resourceDetail = async (data) => {
    return await $http({
        url: 'dataView/resource/detail',
        method: 'post',
        data
    })
}

/**
 * 工作动态详情
 */
export const workDynamicDetail = async (data) => {
    return await $http({
        url: 'dataView/workDynamic/detail',
        method: 'post',
        data
    })
}

/**
 * 公告详情
 */
export const noticeDetail = async (data) => {
    return await $http({
        url: 'dataView/notice/detail',
        method: 'post',
        data
    })
}

/**
 * 新增资料库
 */
export const resourceAdd = async (data) => {
    return await $http({
        url: 'dataView/resource/add',
        method: 'post',
        data
    })
}

/**
 * 新增资料库
 */
export const resourceEdit = async (data) => {
    return await $http({
        url: 'dataView/resource/edit',
        method: 'post',
        data
    })
}
/**
 *资料类型
 */
export const resourceType = async (data) => {
    return await $http({
        url: 'dataView/resource/getType',
        method: 'post',
        data
    })
}

/**
 * 新增公告
 */
export const noticeAdd = async (data) => {
    return await $http({
        url: 'dataView/notice/add',
        method: 'post',
        data
    })
}

/**
 * 编辑公告
 */
export const noticeEdit = async (data) => {
    return await $http({
        url: 'dataView/notice/edit',
        method: 'post',
        data
    })
}

/**
 * 新增工作计划
 */
export const workPlanAdd = async (data) => {
    return await $http({
        url: 'dataView/workPlanAdd',
        method: 'post',
        data
    })
}
/**
 * 编辑工作计划
 */
export const workPlanEdit = async (data) => {
    return await $http({
        url: 'dataView/workPlanEdit',
        method: 'post',
        data
    })
}
/**
 * 新增工作动态
 */
export const workDynamicAdd = async (data) => {
    return await $http({
        url: 'dataView/workDynamic/add',
        method: 'post',
        data
    })
}

/**
 * 编辑工作动态
    */
export const workDynamicEdit = async (data) => {
    return await $http({
        url: 'dataView/workDynamic/edit',
        method: 'post',
        data
    })
}
/**
 * 驻村补助发放记录
 */
export const userSentLog = async (data) => {
    return await $http({
        url: 'dataView/userSentLog',
        method: 'post',
        data
    })
}
/**
 * 帮扶新增
 */
export const helpAdd = async (data) => {
    return await $http({
        url: 'dataView/help/add',
        method: 'post',
        data
    })
}
/**
 * 帮扶编辑
 */
export const helpEdit = async (data) => {
    return await $http({
        url: 'dataView/help/edit',
        method: 'post',
        data
    })
    }

    /**
 * 帮扶详情
 */
export const helpDetail = async (data) => {
    return await $http({
        url: 'dataView/help/detail',
        method: 'post',
        data
    })
    }
/**
 * 新增关爱
 */
export const addCarePolicy = async (data) => {
    return await $http({
        url: 'dataView/addCarePolicy',
        method: 'post',
        data
    })
}

/**
 * 资金绩效经费类型下拉框
 */
export const preApplyFundsData = async (data) => {
    return await $http({
        url: 'dataView/preApplyFundsData',
        method: 'post',
        data
    })
}


/**
 * 新增资金绩效
 */
export const fundsApply = async (data) => {
    return await $http({
        url: 'dataView/fundsApply',
        method: 'post',
        data
    })
}
/**
 * 新增资金绩效删除
 */
export const fundsDelete = async (data) => {
    return await $http({
        url: 'dataView/funds/delete',
        method: 'post',
        data
    })
}


/**
 * 统计报表新增模板
 */
export const totalAddTemplate = async (data) => {
    return await $http({
        url: 'total/addTemplate',
        method: 'post',
        data
    })
}
/**
 * 统计报表模板
 */
export const totalTemplate = async (data) => {
    return await $http({
        url: 'total/template',
        method: 'post',
        data
    })
}


/**
 * 村详情
 */
export const villageDetail = async (data) => {
    return await $http({
        url: 'dataView/village/detail',
        method: 'post',
        data
    })
}


/**
 * 修改模板
 */
export const editTemplate = async (data) => {
    return await $http({
        url: 'total/editTemplate',
        method: 'post',
        data
    })
}
/**
 * 删除模板
 */


export const deleteTemplate = async (data) => {
    return await $http({
        url: 'total/deleteTemplate',
        method: 'post',
        data
    })
}

/**
 * 获取统计表列表
 */
export const getTotalList = async (data) => {
    return await $http({
        url: 'total/getTotalList',
        method: 'post',
        data
    })
}

/**
 * 上传文件
 */
export const uploads = async (data) => {
    return await $http({
        url: 'total/uploads',
        method: 'post',
        data
    })
}
// /**
//  * 获取所有文章
//  * @param {number} page - 页码
//  * @param {number} pageSize - 每页条数
//  * @returns {Promise} API响应
//  */
// export const getAllArticleApi = async (page = 1, pageSize = 10) => {
//     return await $http({
//         url: '/article/get-all-article',
//         method: 'GET',
//         params: {
//             page,
//             pageSize
//         }
//     })
// }
