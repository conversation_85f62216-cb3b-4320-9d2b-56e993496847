<script setup>
import { RouterView } from "vue-router";
import { watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

watch(() => route.path, () => {
    setTimeout(() => {
        window.scrollTo(0, 0)
        document.documentElement.scrollTop = 0
        document.body.scrollTop = 0
    }, 50)
}, { immediate: true })
</script>

<template>
    <div class="app-container">
        <router-view />
    </div>
</template>

<style lang="less">
html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    min-height: 100%;
}

.app-container {
    width: 100%;
    min-height: 100vh;
}
</style>
