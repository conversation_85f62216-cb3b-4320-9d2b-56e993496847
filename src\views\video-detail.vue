<template>
    <div class="video-detail-page">
        <!-- 顶部导航 -->
        <!-- <div class="top-nav">
            <div class="nav-container">
                <div class="logo">
                    <img src="https://picsum.photos/120/40?random=100" alt="EIR" />
                </div>
                <div class="nav-center">
                    <div class="nav-links">
                        <router-link to="/" class="nav-link">首页</router-link>
                        <router-link to="/about" class="nav-link">关于我们</router-link>
                        <router-link to="/products" class="nav-link">主要产品</router-link>
                        <router-link to="/videos" class="nav-link" :class="isVideosActive ? 'active' : ''">视频演示</router-link>
                        <router-link to="/news" class="nav-link">新闻动态</router-link>
                        <router-link to="/tech" class="nav-link">技术支持</router-link>
                        <div class="nav-link language-selector">
                            EN
                            <i class="arrow-down"></i>
                        </div>
                    </div>
                </div>
                <div class="contact-btn">
                    联系我们
                </div>
            </div>
        </div> -->

        <!-- 视频播放区域 -->
        <div class="video-hero">
            <div class="video-background">
                <img src="https://picsum.photos/1200/600?random=1" alt="视频背景" />
                <div class="video-overlay">
                    <div class="video-content">
                        <h1 class="video-title">或许</h1>
                        <p class="video-subtitle">人类真正渴望的，不是冷冰冰的工具，而是有温度的陪伴</p>
                        <p class="video-description">
                            《星球大战》里的 C-3PO 、《机器人总动员》里的瓦力、《超能陆战队》里的大白，一个个有血有肉的机器人形象，
                            深深印在我们心中。或许，这就是我们对机器人的终极想象。
                        </p>
                        <div class="play-button-large">
                            <i class="play-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细内容 -->
        <div class="content-section">
            <div class="content-container">
                <h2 class="section-title">我们的一体化关节、分层运控架构，情感驱动引擎已授权20多项专利，发表论文100余篇与电子科技大学、天府缘溪实验室合作，持续优化算法，建设数采工厂</h2>
                
                <div class="content-text">
                    <p>这个世界充满挑战，每一个挑战都是我们前进的动力。我们相信，只有不断创新，才能在激烈的竞争中脱颖而出。我们的团队由一群充满激情的工程师组成，他们来自世界各地，拥有丰富的经验和深厚的技术功底。</p>
                    
                    <p>我们的产品不仅仅是一个机器人，更是一个智能伙伴。它能够理解人类的情感，能够与人类进行自然的交流，能够在各种复杂的环境中自主工作。我们相信，这样的机器人将会改变人类的生活方式，让我们的生活更加美好。</p>
                    
                    <p>为了实现这个目标，我们投入了大量的研发资源，与多家知名大学和研究机构建立了合作关系。我们的研发团队在人工智能、机器学习、计算机视觉、自然语言处理等领域都有着深厚的积累。</p>
                    
                    <p>我们的技术优势主要体现在以下几个方面：首先是我们独创的一体化关节设计，这种设计大大提高了机器人的运动精度和稳定性；其次是我们的分层运控架构，这种架构使得机器人能够在复杂的环境中快速做出决策；最后是我们的情感驱动引擎，这个引擎让机器人能够理解和表达情感，与人类建立更深层次的连接。</p>
                    
                    <p>目前，我们已经获得了20多项专利，发表了100余篇学术论文。我们与电子科技大学、天府缘溪实验室等知名机构建立了长期的合作关系，共同推进机器人技术的发展。同时，我们还在建设自己的数据采集工厂，为算法的持续优化提供强有力的支撑。</p>
                    
                    <p>展望未来，我们将继续加大研发投入，不断完善我们的技术体系。我们相信，通过我们的努力，机器人将会成为人类最好的伙伴，为人类创造更加美好的未来。</p>
                    
                    <p>我们的愿景是让每个家庭都能拥有一个智能机器人伙伴，让科技真正服务于人类的幸福生活。这不仅仅是一个技术目标，更是我们对未来生活的美好憧憬。</p>
                    
                    <p>在这个充满无限可能的时代，我们邀请更多的合作伙伴加入我们，共同创造机器人技术的美好未来。让我们一起，用科技的力量，点亮人类的梦想。</p>
                </div>
            </div>
        </div>

        <!-- 底部联系信息
        <div class="footer">
            <div class="footer-content">
                <div class="footer-left">
                    <h3>联系我们</h3>
                    <p>官方网站：www.eir.com.cn</p>
                    <p>咨询电话：400-xxx-xxxx</p>
                    <p>人才邮箱：<EMAIL></p>
                    <p>公司地址：四川省成都市高新区XXXXXXXXXXXXXXX</p>
                </div>
                <div class="footer-center">
                    <h3>订阅咨询</h3>
                    <p>Email: <EMAIL></p>
                    <p>(+86)28 2534 8639</p>
                    <p>(+86)755 2399 1467</p>
                </div>
                <div class="footer-right">
                    <div class="qr-code">
                        <img src="https://picsum.photos/100/100?random=1" alt="二维码" />
                        <p>联系我们</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2024 四川省成都市高新区 蜀ICP备XXXXXXXX号</p>
            </div>
        </div> -->
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const videoId = ref(route.params.id)

// 判断是否在视频相关页面
const isVideosActive = computed(() => {
    return route.path.includes('/videos')
})

onMounted(() => {
    console.log('视频详情页面加载，视频ID:', videoId.value)
})
</script>

<style lang="less" scoped>
.video-detail-page {
    min-height: 100vh;
    background: #f5f5f5;

    .top-nav {
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 0;
        z-index: 100;

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            height: 70px;

            .logo {
                img {
                    height: 40px;
                }
            }

            .nav-center {
                flex: 1;
                display: flex;
                justify-content: center;

                .nav-links {
                    display: flex;
                    gap: 40px;
                    align-items: center;

                    .nav-link {
                        color: #666666;
                        text-decoration: none;
                        font-size: 20px;
                        font-weight: 400;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        transition: color 0.3s;

                        &:hover {
                            color: #555;
                        }

                        &.router-link-active, &.active {
                            font-weight: bold;
                            color: #1A1A1A;
                        }
                    }

                    .language-selector {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        cursor: pointer;

                        .arrow-down {
                            width: 0;
                            height: 0;
                            border-left: 4px solid transparent;
                            border-right: 4px solid transparent;
                            border-top: 4px solid #333;
                        }
                    }
                }
            }

            .contact-btn {
                background: #333;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.3s;

                &:hover {
                    background: #555;
                }
            }
        }
    }

    .video-hero {
        height: 100vh;
        position: relative;
        overflow: hidden;

        .video-background {
            width: 100%;
            height: 100%;
            position: relative;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .video-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                align-items: center;
                justify-content: center;

                .video-content {
                    text-align: center;
                    color: white;
                    max-width: 800px;
                    padding: 0 20px;

                    .video-title {
                        font-size: 72px;
                        font-weight: bold;
                        margin-bottom: 20px;
                        letter-spacing: 8px;
                    }

                    .video-subtitle {
                        font-size: 24px;
                        margin-bottom: 30px;
                        line-height: 1.6;
                        opacity: 0.9;
                    }

                    .video-description {
                        font-size: 16px;
                        line-height: 1.8;
                        margin-bottom: 50px;
                        opacity: 0.8;
                        max-width: 600px;
                        margin-left: auto;
                        margin-right: auto;
                    }

                    .play-button-large {
                        width: 80px;
                        height: 80px;
                        background: rgba(255, 255, 255, 0.9);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto;
                        cursor: pointer;
                        transition: all 0.3s;

                        &:hover {
                            background: white;
                            transform: scale(1.1);
                        }

                        .play-icon {
                            width: 0;
                            height: 0;
                            border-left: 30px solid #333;
                            border-top: 18px solid transparent;
                            border-bottom: 18px solid transparent;
                            margin-left: 6px;
                        }
                    }
                }
            }
        }
    }

    .content-section {
        background: white;
        padding: 80px 0;

        .content-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;

            .section-title {
                font-size: 28px;
                font-weight: bold;
                color: #333;
                line-height: 1.6;
                margin-bottom: 40px;
                text-align: center;
            }

            .content-text {
                p {
                    font-size: 16px;
                    line-height: 1.8;
                    color: #666;
                    margin-bottom: 24px;
                    text-align: justify;
                    text-indent: 2em;
                }
            }
        }
    }

    .footer {
        background: #333;
        color: white;
        padding: 60px 0 20px;

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;

            .footer-left, .footer-center {
                flex: 1;

                h3 {
                    font-size: 18px;
                    margin-bottom: 20px;
                    color: white;
                }

                p {
                    margin: 8px 0;
                    color: #ccc;
                    font-size: 14px;
                }
            }

            .footer-right {
                .qr-code {
                    text-align: center;

                    img {
                        width: 100px;
                        height: 100px;
                        margin-bottom: 10px;
                    }

                    p {
                        color: #ccc;
                        font-size: 14px;
                        margin: 0;
                    }
                }
            }
        }

        .footer-bottom {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #555;

            p {
                color: #999;
                font-size: 12px;
                margin: 0;
            }
        }
    }
}
</style>
