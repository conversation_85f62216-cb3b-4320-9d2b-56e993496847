<template>
    <div class="about-page">
        <!-- 主横幅区域 -->
        <section class="hero-banner">
            <div class="banner-content">
                <h1 class="banner-title">关于我们</h1>
                <p class="banner-subtitle">ABOUT US</p>
            </div>
        </section>

        <!-- 左侧静态盒子 -->
        <div class="left-sidebar">
            <div class="sidebar-item active">目录</div>
            <div class="sidebar-item">公司介绍</div>
            <div class="sidebar-item">核心团队</div>
            <div class="sidebar-item">投资与合作关系</div>
            <div class="sidebar-item">荣誉奖项</div>
            <div class="sidebar-item">人才发展</div>
        </div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 公司介绍 -->
            <section class="content-section company-section">
                <div class="container">
                    <h2 class="section-title">公司介绍</h2>
                    <div class="company-content">
                        <p class="company-text">
                            我们是一家专注于人工智能和机器人技术的创新企业，致力于为全球客户提供最先进的智能解决方案。公司成立以来，始终坚持技术创新和产品质量，在人形机器人、智能制造、人工智能等领域取得了显著成就。我们的使命是通过先进的人工智能技术，让机器人更好地服务于人类社会，推动智能化时代的到来。
                        </p>
                        <p class="company-text">
                            公司拥有一支由顶尖科学家和工程师组成的研发团队，在机器人控制、计算机视觉、自然语言处理等核心技术领域具有深厚的积累。我们始终坚持以客户需求为导向，不断创新和完善产品，为客户提供最优质的服务和解决方案。
                        </p>

                        <!-- 数据统计 -->
                        <div class="stats-container">
                            <div class="stat-item">
                                <div class="stat-number">30+</div>
                                <div class="stat-label">专利技术</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">20+</div>
                                <div class="stat-label">核心团队</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 核心团队 -->
            <section class="content-section team-section">
                <div class="container">
                    <h2 class="section-title">核心团队</h2>
                    <div class="team-content">
                        <p class="team-description">
                            我们拥有一支由资深专家和优秀工程师组成的核心团队，团队成员在人工智能、机器人技术、软件开发等领域拥有丰富的经验和深厚的技术积累。团队致力于推动人工智能和机器人技术的创新发展，为客户提供最优质的产品和服务。
                        </p>
                        <p class="team-description">
                            我们相信，优秀的团队是企业发展的核心动力。公司建立了完善的人才培养体系和激励机制，为团队成员提供良好的发展平台和成长机会，共同推动技术创新，创造美好未来。
                        </p>
                    </div>
                </div>
            </section>

            <!-- 投资与合作关系 -->
            <section class="content-section investors-section">
                <div class="container">
                    <h2 class="section-title">投资与合作关系</h2>
                    <h3 class="subsection-title">投资方</h3>
                    <div class="investors-grid">
                        <div class="investor-item">
                            <img src="https://picsum.photos/200/100?random=1" alt="投资方1" class="investor-logo">
                        </div>
                        <div class="investor-item">
                            <img src="https://picsum.photos/200/100?random=2" alt="投资方2" class="investor-logo">
                        </div>
                        <div class="investor-item">
                            <img src="https://picsum.photos/200/100?random=3" alt="投资方3" class="investor-logo">
                        </div>
                        <div class="investor-item">
                            <img src="https://picsum.photos/200/100?random=4" alt="投资方4" class="investor-logo">
                        </div>
                        <div class="investor-item">
                            <img src="https://picsum.photos/200/100?random=5" alt="投资方5" class="investor-logo">
                        </div>
                        <div class="investor-item">
                            <img src="https://picsum.photos/200/100?random=6" alt="投资方6" class="investor-logo">
                        </div>
                    </div>

                    <!-- 合作伙伴 -->
                    <h3 class="subsection-title">合作伙伴</h3>
                    <div class="partners-grid">
                        <div class="partner-item">
                            <img src="https://picsum.photos/150/80?random=7" alt="合作伙伴1" class="partner-logo">
                        </div>
                        <div class="partner-item">
                            <img src="https://picsum.photos/150/80?random=8" alt="合作伙伴2" class="partner-logo">
                        </div>
                        <div class="partner-item">
                            <img src="https://picsum.photos/150/80?random=9" alt="合作伙伴3" class="partner-logo">
                        </div>
                        <div class="partner-item">
                            <img src="https://picsum.photos/150/80?random=10" alt="合作伙伴4" class="partner-logo">
                        </div>
                        <div class="partner-item">
                            <img src="https://picsum.photos/150/80?random=11" alt="合作伙伴5" class="partner-logo">
                        </div>
                        <div class="partner-item">
                            <img src="https://picsum.photos/150/80?random=12" alt="合作伙伴6" class="partner-logo">
                        </div>
                    </div>
                </div>
            </section>

            <!-- 荣誉奖项 -->
            <section class="content-section honors-section">
                <div class="container">
                    <h2 class="section-title">荣誉奖项</h2>
                    <div class="honors-grid">
                        <div class="honor-item">
                            <img src="https://picsum.photos/300/200?random=13" alt="荣誉证书1" class="honor-image">
                        </div>
                        <div class="honor-item">
                            <img src="https://picsum.photos/300/200?random=14" alt="荣誉证书2" class="honor-image">
                        </div>
                        <div class="honor-item">
                            <img src="https://picsum.photos/300/200?random=15" alt="荣誉证书3" class="honor-image">
                        </div>
                        <div class="honor-item">
                            <img src="https://picsum.photos/300/200?random=16" alt="荣誉证书4" class="honor-image">
                        </div>
                    </div>
                </div>
            </section>

            <!-- 人才发展 -->
            <section class="content-section careers-section">
                <div class="container">
                    <h2 class="section-title">人才发展</h2>
                    <div class="careers-content">
                        <p class="careers-description">
                            我们相信人才是企业最宝贵的财富。公司致力于为员工提供良好的发展平台和成长机会，建立了完善的人才培养体系和激励机制。我们欢迎有志于人工智能和机器人技术发展的优秀人才加入我们的团队，共同推动技术创新，创造美好未来。
                        </p>
                        <p class="careers-description">
                            如果您对我们的事业感兴趣，欢迎投递简历，我们期待与您一起开创智能化时代的美好未来。
                        </p>
                    </div>
                </div>
            </section>
        </main>
    </div>
</template>

<script setup>
// 关于我们页面逻辑
</script>

<style lang="scss" scoped>
.about-page {
    background: #fff;
    position: relative;

    // 主横幅区域
    .hero-banner {
        height: 400px;
        background-image: url('https://picsum.photos/1920/400?random=1');
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.4);
        }

        .banner-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;

            .banner-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: bold;
                font-size: 48px;
                color: #FFFFFF;
                margin-bottom: 16px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }

            .banner-subtitle {
                font-family: Arial, sans-serif;
                font-weight: 400;
                font-size: 20px;
                color: #E8E8E8;
                margin: 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }
        }
    }

    // 左侧静态盒子
    .left-sidebar {
        position: fixed;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        width: 120px;
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 100;

        .sidebar-item {
            padding: 12px 16px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-size: 14px;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;

            &:last-child {
                border-bottom: none;
            }

            &.active {
                background: #f8f9fa;
                color: #333;
                font-weight: 500;
            }

            &:hover {
                background: #f8f9fa;
                color: #333;
            }
        }
    }

    // 主要内容区域
    .main-content {
        padding-left: 160px; // 为左侧盒子留出空间

        // 内容区域
        .content-section {
            padding: 60px 0;

            &:nth-child(even) {
                background: #f8f9fa;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 40px;
            }

            .section-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 28px;
                color: #1A1A1A;
                margin-bottom: 30px;
            }

            .subsection-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 20px;
                color: #333;
                margin: 40px 0 20px 0;
            }
        }

        // 公司介绍区域
        .company-section {
            .company-content {
                .company-text {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-size: 16px;
                    color: #666;
                    line-height: 1.8;
                    margin-bottom: 20px;
                    text-align: justify;
                }

                .stats-container {
                    display: flex;
                    justify-content: center;
                    gap: 100px;
                    margin-top: 40px;

                    .stat-item {
                        text-align: center;

                        .stat-number {
                            font-family: Arial, sans-serif;
                            font-weight: bold;
                            font-size: 48px;
                            color: #007bff;
                            margin-bottom: 8px;
                        }

                        .stat-label {
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-size: 16px;
                            color: #666;
                        }
                    }
                }
            }
        }

        // 核心团队区域
        .team-section {
            .team-content {
                .team-description {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-size: 16px;
                    color: #666;
                    line-height: 1.8;
                    margin-bottom: 20px;
                    text-align: justify;
                }
            }
        }

        // 投资与合作关系区域
        .investors-section {
            .investors-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20px;
                margin-bottom: 40px;

                .investor-item {
                    background: #fff;
                    padding: 20px;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 80px;

                    .investor-logo {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }
                }
            }

            .partners-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;

                .partner-item {
                    background: #fff;
                    padding: 15px;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 60px;

                    .partner-logo {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }
                }
            }
        }

        // 荣誉奖项区域
        .honors-section {
            .honors-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;

                .honor-item {
                    background: #fff;
                    border-radius: 8px;
                    overflow: hidden;
                    border: 1px solid #e9ecef;

                    .honor-image {
                        width: 100%;
                        height: 200px;
                        object-fit: cover;
                    }
                }
            }
        }

        // 人才发展区域
        .careers-section {
            .careers-content {
                .careers-description {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-size: 16px;
                    color: #666;
                    line-height: 1.8;
                    margin-bottom: 20px;
                    text-align: justify;
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1024px) {
        .left-sidebar {
            display: none;
        }

        .main-content {
            padding-left: 0;

            .content-section .container {
                padding: 0 30px;
            }

            .investors-section {
                .investors-grid {
                    grid-template-columns: repeat(2, 1fr);
                }

                .partners-grid {
                    grid-template-columns: repeat(3, 1fr);
                }
            }

            .company-section .company-content .stats-container {
                gap: 60px;
            }
        }
    }

    @media (max-width: 768px) {
        .hero-banner {
            height: 300px;

            .banner-title {
                font-size: 36px;
            }

            .banner-subtitle {
                font-size: 16px;
            }
        }

        .main-content {
            .content-section {
                padding: 40px 0;

                .container {
                    padding: 0 20px;
                }

                .section-title {
                    font-size: 24px;
                }
            }

            .company-section .company-content .stats-container {
                flex-direction: column;
                gap: 30px;
            }

            .investors-section {
                .investors-grid {
                    grid-template-columns: 1fr;
                    gap: 15px;
                }

                .partners-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            .honors-section .honors-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    }

    @media (max-width: 480px) {
        .hero-banner {
            height: 250px;

            .banner-title {
                font-size: 28px;
            }

            .banner-subtitle {
                font-size: 14px;
            }
        }

        .main-content {
            .content-section {
                padding: 30px 0;

                .section-title {
                    font-size: 20px;
                }
            }

            .investors-section .partners-grid {
                grid-template-columns: 1fr;
            }
        }
    }
}
</style>
