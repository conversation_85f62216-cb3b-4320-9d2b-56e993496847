<template>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <!-- Logo区域 -->
            <div class="logo">
                <img src="/src/assets/imgs/LOGO_1.png" alt="EIR Logo" />
            </div>
            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <ul class="nav-items">
                    <li class="nav-item" :class="$route.path === '/' ? 'active' : ''">
                        <a href="#" @click.prevent.stop="goToPage('/')">首页</a>
                    </li>
                    <li class="nav-item" :class="$route.path.includes('/about') ? 'active' : ''" @mouseenter="showAboutDropdown" @mouseleave="hideAboutDropdown">
                        <a href="#" @click.prevent.stop="goToPage('/about')">关于我们</a>
                    </li>
                    <li class="nav-item" :class="$route.path.includes('/products') ? 'active' : ''" @mouseenter="showProductsDropdown" @mouseleave="hideProductsDropdown">
                        <a href="#" @click.prevent.stop="goToPage('/products')">主要产品</a>
                    </li>
                    <li class="nav-item" :class="$route.path.includes('/videos') ? 'active' : ''">
                        <a href="#" @click.prevent.stop="goToPage('/videos')">视频演示</a>
                    </li>
                    <li class="nav-item" :class="$route.path.includes('/news') ? 'active' : ''">
                        <a href="#" @click.prevent.stop="goToPage('/news')">新闻动态</a>
                    </li>
                    <li class="nav-item" :class="$route.path.includes('/support') ? 'active' : ''" @mouseenter="showSupportDropdown" @mouseleave="hideSupportDropdown">
                        <a href="#" @click.prevent.stop="goToPage('/support')">技术支持</a>
                    </li>
                </ul>
            </nav>

            <!-- 右侧功能区 -->
            <div class="header-actions">
                <!-- 语言切换下拉框 -->
                <div class="language-dropdown" @click="toggleLanguageDropdown">
                    <div class="language-selected">
                        <span>{{ currentLang === 'zh' ? 'ZH' : 'EN' }}</span>
                        <img src="/src/assets/imgs/<EMAIL>" alt="dropdown arrow" class="dropdown-arrow"
                            :class="isLanguageDropdownOpen ? 'rotate' : ''" />
                    </div>
                    <div class="language-options" :class="isLanguageDropdownOpen ? 'show' : ''">
                        <div class="language-option" :class="currentLang === 'zh' ? 'active' : ''"
                            @click.stop="switchLanguage('zh')">
                            CN
                        </div>
                        <div class="language-option" :class="currentLang === 'en' ? 'active' : ''"
                            @click.stop="switchLanguage('en')">
                            EN
                        </div>
                    </div>
                </div>
                <!-- 联系我们按钮 -->
                <div class="contact-button" @click="goToPage('/consult')" 
                     :class="currentRoute === '/consult' ? 'active' : ''">
                    订购咨询
                </div>
            </div>
        </div>
    </header>
    
    <!-- 关于我们下拉菜单 -->
    <section class="about-dropdown-menu" :class="isAboutDropdownVisible ? 'show' : ''"
        @mouseenter="showAboutDropdown" @mouseleave="hideAboutDropdown">
        <div class="dropdown-content">
            <div class="dropdown-column">
                <div class="dropdown-item" @click="goToPage('/about/company')">
                    公司介绍
                </div>
                <div class="dropdown-item" @click="goToPage('/about/team')">
                    核心团队
                </div>
            </div>
            <div class="dropdown-column">
                <div class="dropdown-item" @click="goToPage('/about/investors')">
                    投资者关系
                </div>
                <div class="dropdown-item" @click="goToPage('/about/awards')">
                    荣誉奖项
                </div>
            </div>
            <div class="dropdown-column">
                <div class="dropdown-item" @click="goToPage('/about/careers')">
                    人才发展
                </div>
            </div>
        </div>
    </section>
    
    <!-- 主要产品下拉菜单 -->
    <section class="products-dropdown-menu" :class="isProductsDropdownVisible ? 'show' : ''"
        @mouseenter="showProductsDropdown" @mouseleave="hideProductsDropdown">
        <div class="dropdown-content">
            <div class="product-item" @click="goToPage('/products/skywalker')">
                <img src="/src/assets/imgs/1.png" alt="天行者SKYWALKER人形机器人" class="product-image" />
                <view class="product-title">天行者SKYWALKER人形机器人</view>
                <view class="product-subtitle">了解更多</view>
            </div>
            <div class="product-item coming-soon">
                <img src="/src/assets/imgs/2.png" alt="敬请期待" class="product-image" />
                <view class="product-info">
                    <view class="product-title">敬请期待</view>
                </view>
            </div>
        </div>
    </section>
    
    <!-- 技术支持下拉菜单 -->
    <section class="support-dropdown-menu" :class="isSupportDropdownVisible ? 'show' : ''"
        @mouseenter="showSupportDropdown" @mouseleave="hideSupportDropdown">
        <div class="dropdown-content">
            <div class="dropdown-item" @click="goToPage('/support/docs')">
                开发文档
            </div>
            <div class="dropdown-item" @click="goToPage('/support/robot-as')">
                机器人"AS"服务
            </div>
        </div>
    </section>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 当前路由
const currentRoute = computed(() => route.path)

// 当前语言
const currentLang = ref('zh')
// 下拉框开关状态
const isLanguageDropdownOpen = ref(false)
const isAboutDropdownVisible = ref(false)
const isProductsDropdownVisible = ref(false)
const isSupportDropdownVisible = ref(false)

// 语言切换
const switchLanguage = (lang) => {
    currentLang.value = lang
    isLanguageDropdownOpen.value = false
    console.log('切换语言到:', lang)
}

// 关于我们下拉菜单控制
const showAboutDropdown = () => {
    isAboutDropdownVisible.value = true
}

const hideAboutDropdown = () => {
    isAboutDropdownVisible.value = false
}

// 主要产品下拉菜单控制
const showProductsDropdown = () => {
    isProductsDropdownVisible.value = true
}

const hideProductsDropdown = () => {
    isProductsDropdownVisible.value = false
}

// 技术支持下拉菜单控制
const showSupportDropdown = () => {
    isSupportDropdownVisible.value = true
}

const hideSupportDropdown = () => {
    isSupportDropdownVisible.value = false
}

// 切换下拉框显示状态
const toggleLanguageDropdown = () => {
    isLanguageDropdownOpen.value = !isLanguageDropdownOpen.value
}

// 点击外部关闭下拉框
const handleClickOutside = (event) => {
    const dropdown = document.querySelector('.language-dropdown')
    if (dropdown && !dropdown.contains(event.target)) {
        isLanguageDropdownOpen.value = false
    }
}

// 页面跳转
const goToPage = (path) => {
    console.log('跳转到:', path)
    router.push(path).then(() => {
        setTimeout(() => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            })
        }, 100)
    })
}



// 生命周期钩子
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
// 头部导航
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #fff;
    border-bottom: 2px solid #CDCDCD;

    .header-content {
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 160px;
        height: 96px;

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;

            img {
                width: 218px;
                height: 40px;
                object-fit: cover;
            }
        }

        .nav-menu {
            margin-left: 200px;

            .nav-items {
                display: flex;
                list-style: none;
                margin: 0;
                padding: 0;
                gap: 80px;

                .nav-item {
                    position: relative;
                    a {
                        text-decoration: none;
                        color: #333;
                        font-size: 16px;
                        font-weight: 500;
                        padding: 10px 0;
                        transition: color 0.3s ease;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 20px;
                        color: #666666;

                        &:hover {
                            color: #555;
                        }
                    }

                    &.active a {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: bold;
                        font-size: 20px;
                        color: #1A1A1A;
                    }
                }
            }
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 43px;

            .language-dropdown {
                position: relative;
                cursor: pointer;
                margin-top: 3px;

                .language-selected {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    color: #666;
                    font-size: 14px;
                    font-weight: 500;
                    border-radius: 4px;
                    padding: 8px 12px;
                    transition: all 0.3s ease;

                    &:hover {
                        background: #e9ecef;
                    }

                    .dropdown-arrow {
                        width: 10px;
                        height: 6px;
                        transition: transform 0.3s ease;

                        &.rotate {
                            transform: rotate(180deg);
                        }
                    }
                }

                .language-options {
                    position: absolute;
                    top: calc(100% + 4px);
                    left: 0;
                    background: #fff;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    z-index: 100;
                    display: none;
                    min-width: 100px;
                    padding: 8px 0;

                    &.show {
                        display: block;
                    }

                    .language-option {
                        padding: 8px 12px;
                        cursor: pointer;
                        transition: background-color 0.3s ease;
                        font-size: 14px;
                        color: #333;

                        &:hover {
                            background-color: #f8f9fa;
                        }

                        &.active {
                            font-weight: bold;
                            color: #1A1A1A;
                            background-color: #f0f8ff;
                        }
                    }
                }
            }

            .contact-button {
                width: 117px;
                height: 43px;
                background: #000000;
                border-radius: 24px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 16px;
                color: #FFFFFF;
                text-align: center;
                line-height: 43px;

                &.active {
                    background: #333;
                }

                &:hover {
                    transform: translateY(-1px);
                }
            }
        }
    }
}

// 关于我们下拉菜单
.about-dropdown-menu {
    position: fixed;
    width: 100%;
    top: 98px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 8px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;

    &.show {
        opacity: 1;
        visibility: visible;
    }

    .dropdown-content {
        display: flex;
        justify-content: center;
        gap: 133px;
        padding: 32px 0;
        padding-bottom: 10px;
        padding-left: 400px;

        .dropdown-column {
            .dropdown-item {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 21px;
                color: #1A1A1A;
                cursor: pointer;
                transition: color 0.3s ease;
                margin-bottom: 27px;
            }
        }
    }
}

// 主要产品下拉菜单
.products-dropdown-menu {
    position: fixed;
    width: 100%;
    top: 98px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 8px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;

    &.show {
        opacity: 1;
        visibility: visible;
    }

    .dropdown-content {
        display: flex;
        justify-content: center;
        gap: 27px;
        padding: 27px 0;

        .product-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #FAFAFA;
            width: 347px;
            height: 373px;

            .product-image {
                width: 213px;
                height: 213px;
                margin-bottom: 28px;
            }

            .product-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 21px;
                color: #1A1A1A;
                margin-bottom: 20px;
            }

            .product-subtitle {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 300;
                font-size: 16px;
                color: #333333;
            }
        }
    }
}

// 技术支持下拉菜单
.support-dropdown-menu {
    position: fixed;
    width: 100%;
    top: 98px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 8px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;

    &.show {
        opacity: 1;
        visibility: visible;
    }

    .dropdown-content {
        display: flex;
        flex-direction: column;
        gap: 27px;
        padding: 32px 0;
        padding-left: 1365px;

        .dropdown-item {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 21px;
            color: #1A1A1A;
            cursor: pointer;
            transition: color 0.3s ease;
        }
    }
}
</style>
