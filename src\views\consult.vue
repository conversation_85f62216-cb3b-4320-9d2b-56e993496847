<template>
    <div class="consult-page">

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="consult-container">
                <h1 class="page-title">订购咨询</h1>
                <view class="background-text">CONSULT</view>
                
                <form class="consult-form" @submit.prevent="submitForm">
                    <div class="form-row">
                        <div class="form-group">
                            <input 
                                type="text" 
                                v-model="formData.company" 
                                placeholder="请输入公司/机构名称"
                                class="form-input"
                            />
                            <label class="form-label">公司/机构名称</label>
                        </div>
                        <div class="form-group">
                            <input 
                                type="text" 
                                v-model="formData.position" 
                                placeholder="请输入职位名称"
                                class="form-input"
                            />
                            <label class="form-label">职位名称</label>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <input 
                                type="text" 
                                v-model="formData.name" 
                                placeholder="请输入姓名"
                                class="form-input"
                            />
                            <label class="form-label">姓名</label>
                        </div>
                        <div class="form-group">
                            <input 
                                type="tel" 
                                v-model="formData.phone" 
                                placeholder="请输入电话"
                                class="form-input"
                            />
                            <label class="form-label">电话</label>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <input 
                                type="email" 
                                v-model="formData.email" 
                                placeholder="请输入电子邮箱"
                                class="form-input"
                            />
                            <label class="form-label">电子邮箱</label>
                        </div>
                        <div class="form-group">
                            <select v-model="formData.city" class="form-select">
                                <option value="">请选择所在城市</option>
                                <option value="北京">北京</option>
                                <option value="上海">上海</option>
                                <option value="广州">广州</option>
                                <option value="深圳">深圳</option>
                                <option value="成都">成都</option>
                                <option value="其他">其他</option>
                            </select>
                            <label class="form-label">所在城市</label>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <select v-model="formData.product" class="form-select">
                                <option value="">请选择感兴趣产品</option>
                                <option value="天行者SKYWALKER人形机器人">天行者SKYWALKER人形机器人</option>
                                <option value="其他产品">其他产品</option>
                            </select>
                            <label class="form-label">意向产品</label>
                        </div>
                        <div class="form-group">
                            <input 
                                type="text" 
                                v-model="formData.usage" 
                                placeholder="请输入用途"
                                class="form-input"
                            />
                            <label class="form-label">主要用途</label>
                        </div>
                    </div>

                    <div class="form-row full-width">
                        <div class="form-group">
                            <textarea 
                                v-model="formData.source" 
                                placeholder="请输入"
                                class="form-textarea"
                                rows="3"
                            ></textarea>
                            <label class="form-label">从何处获悉我们</label>
                        </div>
                        <div class="form-group">
                            <textarea 
                                v-model="formData.suggestion" 
                                placeholder="请输入意见建议"
                                class="form-textarea"
                                rows="3"
                            ></textarea>
                            <label class="form-label">其他需求建议</label>
                        </div>
                    </div>

                    <div class="form-footer">
                        <view class="privacy-notice">
                            <view>如可以提供联系我们以获得更多信息了解;</view>
                            <view>在您提交以上信息会在24小时内向您回复您想要了解的信息，所以请确保您的联系方式准确无误，以确保我们能够。</view>
                        </view>
                        <button type="submit" class="submit-btn">提交</button>
                    </div>
                </form>
            </div>
        </main>
    </div>
</template>

<script setup>
import { ref } from 'vue'

// 表单数据
const formData = ref({
    company: '',
    position: '',
    name: '',
    phone: '',
    email: '',
    city: '',
    product: '',
    usage: '',
    source: '',
    suggestion: ''
})



// 提交表单
const submitForm = () => {
    console.log('提交表单:', formData.value)
    // 这里添加表单提交逻辑
    uni.showToast({
        title: '提交成功',
        icon: 'success'
    })
}
</script>

<style lang="scss" scoped>
.consult-page {
    min-height: 100vh;
    background: #f8f9fa;
}



// 主要内容区域
.main-content {
    padding: 80px 24px;
    max-width: 1200px;
    margin: 0 auto;

    .consult-container {
        position: relative;
        background: #fff;
        border-radius: 16px;
        padding: 60px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        .page-title {
            font-size: 48px;
            font-weight: bold;
            color: #333;
            margin: 0 0 60px 0;
            font-family: Source Han Sans CN, Source Han Sans CN;
        }

        .background-text {
            position: absolute;
            top: 40px;
            left: 60px;
            font-size: 120px;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.03);
            z-index: 1;
            pointer-events: none;
            font-family: Arial, sans-serif;
        }

        .consult-form {
            position: relative;
            z-index: 2;

            .form-row {
                display: flex;
                gap: 32px;
                margin-bottom: 32px;

                &.full-width {
                    .form-group {
                        flex: 1;
                    }
                }

                .form-group {
                    flex: 1;
                    position: relative;

                    .form-label {
                        display: block;
                        font-size: 16px;
                        color: #333;
                        margin-bottom: 8px;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                    }

                    .form-input,
                    .form-select,
                    .form-textarea {
                        width: 100%;
                        padding: 16px;
                        border: 1px solid #e0e0e0;
                        border-radius: 8px;
                        font-size: 14px;
                        color: #333;
                        background: #fff;
                        transition: border-color 0.3s ease;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        box-sizing: border-box;

                        &:focus {
                            outline: none;
                            border-color: #007bff;
                        }

                        &::placeholder {
                            color: #999;
                        }
                    }

                    .form-select {
                        cursor: pointer;
                        appearance: none;
                        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                        background-position: right 12px center;
                        background-repeat: no-repeat;
                        background-size: 16px;
                        padding-right: 40px;
                    }

                    .form-textarea {
                        resize: vertical;
                        min-height: 80px;
                    }
                }
            }

            .form-footer {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                margin-top: 48px;

                .privacy-notice {
                    flex: 1;
                    font-size: 14px;
                    color: #666;
                    line-height: 1.6;
                    margin-right: 32px;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                }

                .submit-btn {
                    background: #000;
                    color: #fff;
                    border: none;
                    border-radius: 32px;
                    padding: 16px 48px;
                    font-size: 16px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-family: Source Han Sans CN, Source Han Sans CN;

                    &:hover {
                        background: #333;
                        transform: translateY(-2px);
                    }
                }
            }
        }
    }
}



// 响应式设计
@media (max-width: 768px) {
    .main-content {
        padding: 40px 16px;

        .consult-container {
            padding: 32px 24px;

            .page-title {
                font-size: 32px;
                margin-bottom: 40px;
            }

            .background-text {
                font-size: 80px;
                left: 24px;
                top: 20px;
            }

            .consult-form {
                .form-row {
                    flex-direction: column;
                    gap: 24px;
                }

                .form-footer {
                    flex-direction: column;
                    gap: 24px;
                    align-items: stretch;

                    .privacy-notice {
                        margin-right: 0;
                    }
                }
            }
        }
    }


}
</style>
