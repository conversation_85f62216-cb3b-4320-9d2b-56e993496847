<template>
    <!-- 页脚 -->
    <section class="footer-section">
        <div class="footer-container">
            <div class="footer-content">
                <!-- 联系我们 -->
                <div class="footer-contact">
                    <div class="footer-title">联系我们</div>
                    <div class="contact-item">
                        <span class="contact-label">联系邮件：</span>
                        <span class="contact-value"><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label">媒体合作：</span>
                        <span class="contact-value"><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label">人才招聘：</span>
                        <span class="contact-value"><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label">公司地址：</span>
                        <span class="contact-value">四川省成都市高新区XXXXXXXXXX</span>
                    </div>
                </div>
                <!-- 订阅咨询 -->
                <div class="footer-subscribe">
                    <div class="footer-title">订阅咨询</div>
                    <div class="contact-item">
                        <span class="contact-label">Email：</span>
                        <span class="contact-value"><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-value">(+86)180 2598 4639</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-value">(+86)755 2359 6107</span>
                    </div>
                </div>
                <!-- 二维码 -->
                <div class="footer-qrcode">
                    <div class="qrcode-image">
                        <img src="https://picsum.photos/120/120?random=4" alt="二维码" />
                    </div>
                    <div class="qrcode-text">联系我们</div>
                </div>
            </div>
            <!-- 版权信息 -->
            <div class="footer-copyright">
                <div class="copyright-text">
                    Copyright © 2025 蜀ICP备2022XXXXXX号 营业执照编号4XXXXXXXX
                </div>
            </div>
        </div>
    </section>
</template>

<script setup>
// 页脚组件不需要额外的逻辑
</script>

<style lang="scss" scoped>
// 页脚样式
.footer-section {
    background: #1A1A1A;
    color: #fff;
    // padding: 80px 160px;

    .footer-container {
        // max-width: 1200px;
        margin: 0 auto;
        // padding: 0 160px;

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            // padding-bottom: 40px;
            border-bottom: 1px solid #595959;
            padding: 80px 160px;

            .footer-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 32px;
                color: #FFFFFF;
                margin-bottom: 21px;
            }

            // 联系我们
            .footer-contact {
                flex: 1;

                .contact-item {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    font-size: 19px;
                    color: #898989;
                    margin-bottom: 11px;

                    .contact-label {
                        color: #898989;
                    }

                    .contact-value {
                        color: #898989;
                    }
                }
            }

            // 订阅咨询
            .footer-subscribe {
                flex: 1;

                .contact-item {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    font-size: 19px;
                    color: #898989;
                    margin-bottom: 11px;

                    .contact-label {
                        color: #898989;
                    }

                    .contact-value {
                        color: #898989;
                    }
                }
            }

            // 二维码
            .footer-qrcode {
                flex: 0 0 auto;
                text-align: center;

                .qrcode-image {
                    width: 160px;
                    height: 160px;
                    margin-bottom: 12px;
                    border-radius: 8px;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .qrcode-text {
                    font-size: 14px;
                    color: #898989;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                }
            }
        }

        // 版权信息
        .footer-copyright {
            padding: 24px 0;
            text-align: center;

            .copyright-text {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 300;
                font-size: 19px;
                color: #898989;
            }
        }
    }
}
</style>
