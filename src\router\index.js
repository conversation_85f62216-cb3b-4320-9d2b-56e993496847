import { createRouter, createWebHashHistory } from "vue-router";
import { handleRouteScroll } from "../utils/scrollManager";

const routes = [
    {
        path: "/",
        component: () => import("../components/Layout.vue"),
        children: [
            {
                path: "",
                name: "Home",
                component: () => import("../views/index.vue"),
                meta: { title: "首页" },
            },
            {
                path: "consult",
                name: "Consult",
                component: () => import("../views/consult.vue"),
                meta: { title: "订购咨询" },
            },
            // 预留其他页面路由
            {
                path: "about",
                name: "About",
                component: () => import("../views/about.vue"),
                meta: { title: "关于我们" },
            },
            {
                path: "products",
                name: "Products",
                component: () => import("../views/products.vue"),
                meta: { title: "主要产品" },
            },
            {
                path: "news",
                name: "News",
                component: () => import("../views/news.vue"),
                meta: { title: "新闻动态" },
            },
            {
                path: "news/:id",
                name: "NewsDetail",
                component: () => import("../views/news-detail.vue"),
                meta: { title: "新闻详情" },
            },
            {
                path: "videos",
                name: "Videos",
                component: () => import("../views/videos.vue"),
                meta: { title: "视频演示" },
            },
            {
                path: "videos/:id",
                name: "VideoDetail",
                component: () => import("../views/video-detail.vue"),
                meta: { title: "视频详情" },
            },
            {
                path: "support",
                name: "Support",
                component: () => import("../views/support.vue"),
                meta: { title: "技术支持" },
            },
            {
                path: "tech",
                name: "Tech",
                component: () => import("../views/support.vue"),
                meta: { title: "技术支持" },
            },
            {
                path: "scroll-test",
                name: "ScrollTest",
                component: () => import("../views/scroll-test.vue"),
                meta: { title: "滚动测试" },
            },
        ],
    },

    // 404路由
    {
        path: "/:pathMatch(.*)*",
        redirect: "/",
    },
];

export const router = createRouter({
    history: createWebHashHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        // 如果是浏览器前进后退，恢复之前的滚动位置
        if (savedPosition) {
            return savedPosition
        }

        // 使用滚动管理器处理路由切换
        return handleRouteScroll()
    }
});
