<template>
    <div class="support-page">
        <!-- 主横幅区域 -->
        <section class="hero-banner">
            <div class="banner-content">
                <div class="banner-overlay">
                    <h1 class="banner-title">开源筑基，服务护航</h1>
                    <p class="banner-subtitle">Open source foundation building, service escort</p>
                </div>
            </div>
        </section>

        <!-- 介绍文字区域 -->
        <section class="intro-section">
            <div class="intro-container">
                <p class="intro-text">
                    我们针对客户的需求提供定制化的解决方案，包括3D视觉模块SDK、算法接口以及API的定制化文档服务，提供针对客户的开发工具支持，大幅度提高客户开发效率。
                </p>
                <p class="intro-text">
                    同时我们提供人工智能系统服务——从实际问题到解决方案的导向，学习过程中，我们提供产品目标高效服务，让每一个技术人员都能获得系统化服务，每一个产品都能获得更好的技术支持。
                </p>
            </div>
        </section>

        <!-- 开发文档区域 -->
        <section class="docs-section">
            <div class="docs-container">
                <h2 class="section-title">开发文档</h2>
                <div class="docs-content">
                    <p class="docs-description">详细上手，简单易懂</p>
                    <div class="docs-link">
                        <a href="#" class="link-button">查看文档</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 机器人AS服务区域 -->
        <section class="as-service-section">
            <div class="as-container">
                <h2 class="section-title">机器人"AS"服务</h2>
                <div class="service-grid">
                    <div class="service-column">
                        <h3 class="service-category">SALES</h3>
                        <ul class="service-list">
                            <li>销售与支持</li>
                            <li>产品展示与演示平台</li>
                            <li>个性化定制服务</li>
                            <li>维护与保修</li>
                            <li>软件功能定制</li>
                            <li>智能人工客服机器人工具</li>
                        </ul>
                    </div>
                    <div class="service-column">
                        <h3 class="service-category">SPARE PARTS</h3>
                        <ul class="service-list">
                            <li>备件生产制作和销售</li>
                            <li>智能仓储</li>
                            <li>运输仓储平台手册与指引</li>
                            <li>备件生产制作和销售</li>
                        </ul>
                    </div>
                    <div class="service-column">
                        <h3 class="service-category">SURVEY</h3>
                        <ul class="service-list">
                            <li>调研反馈与咨询</li>
                            <li>用户意见反馈平台</li>
                        </ul>
                    </div>
                    <div class="service-column">
                        <h3 class="service-category">SERVICE</h3>
                        <ul class="service-list">
                            <li>全球售后服务</li>
                            <li>定制化服务</li>
                            <li>OTA升级</li>
                            <li>多媒体服务</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系方式区域 -->
        <section class="contact-section">
            <div class="contact-container">
                <h3 class="contact-title">联系方式：</h3>
                <p class="contact-info">邮箱：<EMAIL></p>
            </div>
        </section>
    </div>
</template>

<script setup>
// 技术支持页面逻辑
</script>

<style lang="scss" scoped>
.support-page {
    width: 100%;
    min-height: 100vh;
    background: #fff;

    // 主横幅区域
    .hero-banner {
        position: relative;
        height: 400px;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="20" fill="rgba(255,255,255,0.1)">01010101</text></svg>');
        background-size: 200px 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .banner-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;

            .banner-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: bold;
                font-size: 48px;
                color: #FFFFFF;
                margin-bottom: 16px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }

            .banner-subtitle {
                font-family: Arial, sans-serif;
                font-weight: 400;
                font-size: 20px;
                color: #E8E8E8;
                margin: 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }
        }
    }

    // 介绍文字区域
    .intro-section {
        padding: 80px 0;
        background: #F8F8F8;

        .intro-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 160px;

            .intro-text {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 16px;
                color: #666666;
                line-height: 1.8;
                margin-bottom: 24px;
                text-align: justify;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    // 开发文档区域
    .docs-section {
        padding: 100px 0;
        background: #fff;
        position: relative;

        &::before {
            content: 'DOCUMENTS';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 120px;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.03);
            z-index: 1;
            pointer-events: none;
            font-family: Arial, sans-serif;
        }

        .docs-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 160px;
            position: relative;
            z-index: 2;

            .section-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 40px;
                color: #1A1A1A;
                margin-bottom: 60px;
                text-align: left;
            }

            .docs-content {
                .docs-description {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    font-size: 18px;
                    color: #666666;
                    margin-bottom: 40px;
                }

                .docs-link {
                    .link-button {
                        display: inline-block;
                        padding: 12px 32px;
                        background: #000000;
                        color: #FFFFFF;
                        text-decoration: none;
                        border-radius: 24px;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 16px;
                        transition: all 0.3s ease;

                        &:hover {
                            background: #333333;
                            transform: translateY(-2px);
                        }
                    }
                }
            }
        }
    }

    // 机器人AS服务区域
    .as-service-section {
        padding: 100px 0;
        background: #F8F8F8;
        position: relative;

        &::before {
            content: 'AS SERVICES';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 120px;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.03);
            z-index: 1;
            pointer-events: none;
            font-family: Arial, sans-serif;
        }

        .as-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 160px;
            position: relative;
            z-index: 2;

            .section-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 40px;
                color: #1A1A1A;
                margin-bottom: 80px;
                text-align: left;
            }

            .service-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 60px;

                .service-column {
                    .service-category {
                        font-family: Arial, sans-serif;
                        font-weight: bold;
                        font-size: 18px;
                        color: #000000;
                        margin-bottom: 30px;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }

                    .service-list {
                        list-style: none;
                        padding: 0;
                        margin: 0;

                        li {
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            font-size: 14px;
                            color: #666666;
                            line-height: 1.8;
                            margin-bottom: 12px;
                            position: relative;
                            padding-left: 0;

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
        }
    }

    // 联系方式区域
    .contact-section {
        padding: 60px 0;
        background: #fff;

        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 160px;

            .contact-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 20px;
                color: #1A1A1A;
                margin-bottom: 16px;
            }

            .contact-info {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 16px;
                color: #666666;
                margin: 0;
            }
        }
    }

    // 响应式设计
    @media (max-width: 1024px) {
        .intro-section .intro-container,
        .docs-section .docs-container,
        .as-service-section .as-container,
        .contact-section .contact-container {
            padding: 0 80px;
        }

        .as-service-section .service-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
        }
    }

    @media (max-width: 768px) {
        .hero-banner {
            height: 300px;

            .banner-title {
                font-size: 36px;
            }

            .banner-subtitle {
                font-size: 16px;
            }
        }

        .intro-section .intro-container,
        .docs-section .docs-container,
        .as-service-section .as-container,
        .contact-section .contact-container {
            padding: 0 40px;
        }

        .docs-section .section-title,
        .as-service-section .section-title {
            font-size: 32px;
        }

        .as-service-section .service-grid {
            grid-template-columns: 1fr;
            gap: 30px;
        }
    }

    @media (max-width: 480px) {
        .intro-section .intro-container,
        .docs-section .docs-container,
        .as-service-section .as-container,
        .contact-section .contact-container {
            padding: 0 20px;
        }

        .hero-banner {
            height: 250px;

            .banner-title {
                font-size: 28px;
            }

            .banner-subtitle {
                font-size: 14px;
            }
        }
    }
}
</style>
