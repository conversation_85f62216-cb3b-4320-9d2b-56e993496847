/**
 * 大屏适配方案
 * 设计稿基准宽度为1920px
 */

// 设计稿尺寸
const designWidth = 1920;
// 基准大小
const baseSize = 16;

function setRem() {
  // 当前页面宽度相对于设计稿的缩放比例
  const scale = document.documentElement.clientWidth / designWidth;
  // 设置页面根节点字体大小
  document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
}

// 初始化
setRem();

// 改变窗口大小时重新设置 rem
window.onresize = function() {
  setRem();
};

// 页面显示/隐藏状态改变时重新设置 rem
document.addEventListener('visibilitychange', setRem);

// 移动设备方向改变时重新设置 rem
window.addEventListener('orientationchange', setRem);

// 禁止双击缩放
document.documentElement.addEventListener('touchstart', function (event) {
  if (event.touches.length > 1) {
    event.preventDefault();
  }
}, { passive: false });

// 禁止双指缩放
document.documentElement.addEventListener('touchmove', function (event) {
  if (event.touches.length > 1) {
    event.preventDefault();
  }
}, { passive: false }); 