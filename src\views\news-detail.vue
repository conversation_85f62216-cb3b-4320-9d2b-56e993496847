<template>
    <div class="news-detail">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="container">
                <div class="nav-content">
                    <div class="logo">
                        <img src="https://picsum.photos/120/40?random=1" alt="Logo" />
                    </div>
                    <nav class="nav-menu">
                        <router-link to="/" :class="$route.path === '/' ? 'active' : ''">首页</router-link>
                        <router-link to="/about" :class="$route.path === '/about' ? 'active' : ''">关于我们</router-link>
                        <router-link to="/products" :class="$route.path === '/products' ? 'active' : ''">主要产品</router-link>
                        <router-link to="/news" :class="$route.path.includes('/news') ? 'active' : ''">新闻动态</router-link>
                        <router-link to="/technology" :class="$route.path === '/technology' ? 'active' : ''">技术支持</router-link>
                        <router-link to="/contact" :class="$route.path === '/contact' ? 'active' : ''">联系我们</router-link>
                    </nav>
                </div>
            </div>
        </header>

        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <div class="container">
                <router-link to="/">首页</router-link>
                <span class="separator">/</span>
                <router-link to="/news">新闻动态</router-link>
                <span class="separator">/</span>
                <span class="current">{{ newsDetail.title }}</span>
            </div>
        </div>

        <!-- 新闻详情内容 -->
        <main class="main-content">
            <div class="container">
                <article class="news-article">
                    <header class="article-header">
                        <h1 class="article-title">{{ newsDetail.title }}</h1>
                        <div class="article-meta">
                            <span class="publish-date">{{ newsDetail.publishDate }}</span>
                            <span class="author" v-if="newsDetail.author">作者：{{ newsDetail.author }}</span>
                        </div>
                    </header>
                    
                    <div class="article-content" v-html="newsDetail.content"></div>
                </article>
            </div>
        </main>

        <!-- 底部 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>联系我们</h3>
                        <p>邮箱：<EMAIL></p>
                        <p>电话：+86 123 4567 8900</p>
                        <p>地址：中国·北京市朝阳区</p>
                    </div>
                    <div class="footer-section">
                        <h3>订阅咨询</h3>
                        <p>Email: <EMAIL></p>
                        <p>Tel: +86 010 1234 5678</p>
                        <p>Fax: +86 010 1234 5679</p>
                    </div>
                    <div class="footer-section">
                        <div class="qr-code">
                            <img src="https://picsum.photos/100/100?random=2" alt="二维码" />
                            <p>关注我们</p>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>Copyright © 2024 智能机器人公司版权所有 京ICP备12345678号</p>
                </div>
            </div>
        </footer>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 新闻详情数据
const newsDetail = ref({
    id: '',
    title: '',
    content: '',
    publishDate: '',
    author: ''
})

// 模拟新闻详情数据
const mockNewsDetail = {
    1: {
        id: 1,
        title: '我们的一体化关节、分层运控架构、情感驱动引擎已授权',
        publishDate: '2025.06.05',
        author: '技术团队',
        content: `
            <p>公司智能工程师团队在AI、机器人技术的多年积累基础上，7月4日获得了，上海市科技局、上海市知识产权局联合颁发的《人工智能技术发明专利》（WAIC），《人工智能技术创新发明专利》等多项专利证书。</p>
            
            <p>这些专利涵盖了机器人的核心技术领域，包括一体化关节设计、分层运动控制架构以及情感驱动引擎等关键技术。其中，一体化关节技术实现了机器人关节的高度集成化，大幅提升了运动精度和响应速度；分层运控架构则为机器人提供了更加灵活和智能的运动控制能力。</p>

            <p>特别值得一提的是，我们的情感驱动引擎技术在业界处于领先地位。该技术能够让机器人具备情感感知和表达能力，使机器人与人类的交互更加自然和谐。这项技术的成功应用，标志着我们在人工智能领域又迈出了重要一步。</p>

            <p>Cloud Ginger人工智能系统是公司自主研发的核心技术。</p>

            <p>该系统集成了先进的机器学习算法、自然语言处理技术以及计算机视觉技术，能够为机器人提供强大的智能决策能力。通过Cloud Ginger系统，机器人不仅能够理解和执行复杂的指令，还能够在不同环境中自主学习和适应。</p>

            <div style="text-align: center; margin: 40px 0;">
                <img src="https://picsum.photos/800/400?random=3" alt="机器人技术展示" style="max-width: 100%; height: auto; border-radius: 8px;" />
            </div>

            <p>本次获得的多项专利证书充分体现了CloudGPT Pro、云端AI平台的创新性、实用性和先进性。RobotGPT、人工智能驱动型机器人技术、以及基于一体化智能硬件的先进性，数字化AI"智能+"文件的智能化管理，为用户带来更加便捷的体验。</p>

            <p>未来，我们将继续加大研发投入，不断推进人工智能和机器人技术的创新发展。我们相信，通过持续的技术创新和产品优化，能够为社会带来更多有价值的智能解决方案，推动人工智能技术在各个领域的广泛应用。</p>

            <p>关于智能机器人技术的更多信息，请访问我们的官方网站或联系我们的技术支持团队。</p>
        `
    },
    2: {
        id: 2,
        title: '智能机器人在医疗领域的应用突破',
        publishDate: '2025.05.28',
        author: '研发中心',
        content: `
            <p>近日，我公司研发的智能医疗机器人在多家三甲医院成功部署，标志着我们在医疗机器人领域取得了重大突破。</p>
            
            <p>该医疗机器人集成了最新的AI诊断技术、精密机械臂操作系统以及先进的图像识别算法，能够协助医生进行精确的手术操作和病情诊断。</p>

            <div style="text-align: center; margin: 40px 0;">
                <img src="https://picsum.photos/800/400?random=4" alt="医疗机器人" style="max-width: 100%; height: auto; border-radius: 8px;" />
            </div>

            <p>在临床试验中，该机器人的诊断准确率达到了98.5%，手术辅助成功率达到99.2%，大大提高了医疗服务的质量和效率。</p>
        `
    },
    3: {
        id: 3,
        title: '公司荣获"年度最佳AI创新企业"奖项',
        publishDate: '2025.05.15',
        author: '公关部',
        content: `
            <p>在2025年度人工智能创新大会上，我公司凭借在智能机器人领域的卓越表现，荣获"年度最佳AI创新企业"奖项。</p>
            
            <p>此次获奖充分体现了业界对我们技术实力和创新能力的认可。我们将继续致力于推动人工智能技术的发展和应用。</p>

            <div style="text-align: center; margin: 40px 0;">
                <img src="https://picsum.photos/800/400?random=5" alt="获奖现场" style="max-width: 100%; height: auto; border-radius: 8px;" />
            </div>

            <p>未来，我们将继续加大研发投入，为客户提供更加优质的智能解决方案。</p>
        `
    }
}

// 获取新闻详情
const getNewsDetail = () => {
    const newsId = route.params.id
    const detail = mockNewsDetail[newsId]
    
    if (detail) {
        newsDetail.value = detail
    } else {
        // 如果没有找到对应的新闻，可以跳转到404页面或显示默认内容
        newsDetail.value = {
            id: newsId,
            title: '新闻不存在',
            content: '<p>抱歉，您访问的新闻不存在。</p>',
            publishDate: '',
            author: ''
        }
    }
}

onMounted(() => {
    getNewsDetail()
})
</script>

<style lang="less" scoped>
.news-detail {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

// 顶部导航样式
.header {
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .nav-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 80px;

        .logo img {
            height: 40px;
        }

        .nav-menu {
            display: flex;
            gap: 40px;

            a {
                color: #333333;
                text-decoration: none;
                font-size: 16px;
                font-weight: 500;
                transition: color 0.3s;

                &:hover,
                &.active {
                    color: #007bff;
                }
            }
        }
    }
}

// 面包屑导航
.breadcrumb {
    background: #f8f9fa;
    padding: 16px 0;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;

        a {
            color: #007bff;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }

        .separator {
            color: #666666;
        }

        .current {
            color: #333333;
            font-weight: 500;
        }
    }
}

// 主要内容区域
.main-content {
    flex: 1;
    padding: 40px 0;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .news-article {
        max-width: 800px;
        margin: 0 auto;

        .article-header {
            margin-bottom: 40px;
            text-align: center;

            .article-title {
                font-size: 32px;
                font-weight: 700;
                color: #333333;
                line-height: 1.4;
                margin-bottom: 20px;
            }

            .article-meta {
                display: flex;
                justify-content: center;
                gap: 20px;
                color: #666666;
                font-size: 14px;

                .publish-date {
                    font-weight: 500;
                }
            }
        }

        .article-content {
            font-size: 16px;
            line-height: 1.8;
            color: #333333;

            :deep(p) {
                margin-bottom: 20px;
            }

            :deep(img) {
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                margin: 20px 0;
            }
        }
    }
}

// 底部样式
.footer {
    background: #2c3e50;
    color: #ffffff;
    padding: 40px 0 20px;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .footer-content {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 40px;
        margin-bottom: 30px;

        .footer-section {
            h3 {
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
                color: #ffffff;
            }

            p {
                margin-bottom: 10px;
                color: #bdc3c7;
                font-size: 14px;
            }

            .qr-code {
                text-align: center;

                img {
                    width: 100px;
                    height: 100px;
                    margin-bottom: 10px;
                }

                p {
                    color: #ffffff;
                    font-size: 14px;
                }
            }
        }
    }

    .footer-bottom {
        border-top: 1px solid #34495e;
        padding-top: 20px;
        text-align: center;

        p {
            color: #95a5a6;
            font-size: 12px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .header {
        .nav-content {
            flex-direction: column;
            height: auto;
            padding: 20px 0;

            .nav-menu {
                gap: 20px;
                margin-top: 20px;
            }
        }
    }

    .breadcrumb {
        .container {
            flex-wrap: wrap;
        }
    }

    .main-content {
        .news-article {
            .article-header {
                .article-title {
                    font-size: 24px;
                }

                .article-meta {
                    flex-direction: column;
                    gap: 10px;
                }
            }
        }
    }

    .footer {
        .footer-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
    }
}
</style>
