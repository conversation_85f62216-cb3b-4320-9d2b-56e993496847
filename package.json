{"name": "vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.9.0", "china-map-geojson": "^1.0.4", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-plus": "^2.9.10", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "less": "^4.3.0", "less-loader": "^12.3.0", "postcss": "^8.5.3", "postcss-px2rem": "^0.3.0", "sass-embedded": "^1.89.0", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}