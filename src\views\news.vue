<template>
    <div class="news-page">
        <!-- 顶部banner区域 -->
        <section class="news-banner">
            <div class="banner-content">
                <h1 class="banner-title">新闻动态</h1>
                <div class="banner-subtitle">NEWS</div>
            </div>
        </section>

        <!-- 新闻列表区域 -->
        <section class="news-content">
            <div class="news-container">
                <div class="news-grid">
                    <div v-for="news in newsData" :key="news.id" class="news-item" @click="goToNewsDetail(news.id)">
                        <div class="news-image">
                            <img :src="news.image" :alt="news.title" />
                        </div>
                        <div class="news-info">
                            <div class="news-title">{{ news.title }}</div>
                            <div class="news-description">{{ news.content }}</div>
                            <div class="news-meta">
                                <span class="news-date">{{ news.date }}</span>
                                <span class="news-author">{{ news.author }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页控制 -->
                <div class="pagination">
                    <button class="pagination-btn" :class="currentPage === 1 ? 'disabled' : ''" @click="prevPage">
                        <img src="/src/assets/imgs/Vector@2x (1).png" alt="上一页" />
                    </button>
                    <div class="pagination-info">
                        <span class="current-page">{{ currentPage }}</span>
                        <span class="separator">/</span>
                        <span class="total-pages">{{ totalPages }}</span>
                    </div>
                    <button class="pagination-btn" :class="currentPage === totalPages ? 'disabled' : ''" @click="nextPage">
                        <img src="/src/assets/imgs/<EMAIL>" alt="下一页" />
                    </button>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 分页数据
const currentPage = ref(1)
const totalPages = ref(3)

// 新闻数据
const newsData = ref([
    {
        id: 1,
        image: 'https://picsum.photos/373/280?random=1',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.05',
        author: '技术团队'
    },
    {
        id: 2,
        image: 'https://picsum.photos/373/280?random=2',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.04',
        author: '研发中心'
    },
    {
        id: 3,
        image: 'https://picsum.photos/373/280?random=3',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.03',
        author: '产品团队'
    },
    {
        id: 4,
        image: 'https://picsum.photos/373/280?random=4',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.02',
        author: '技术团队'
    },
    {
        id: 5,
        image: 'https://picsum.photos/373/280?random=5',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.08.01',
        author: '研发中心'
    },
    {
        id: 6,
        image: 'https://picsum.photos/373/280?random=6',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.07.31',
        author: '产品团队'
    },
    {
        id: 7,
        image: 'https://picsum.photos/373/280?random=7',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.07.30',
        author: '技术团队'
    },
    {
        id: 8,
        image: 'https://picsum.photos/373/280?random=8',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.07.29',
        author: '研发中心'
    },
    {
        id: 9,
        image: 'https://picsum.photos/373/280?random=9',
        title: '我们的一体化关节、分层运控架构、情感驱动引擎等核心技术',
        content: '我们的一体化关节，分层运控架构，情感驱动引擎等核心技术，为机器人提供了强大的运动能力和智能交互体验。通过创新的技术架构，实现了更加自然流畅的人机交互。',
        date: '2024.07.28',
        author: '产品团队'
    }
])

// 跳转到新闻详情
const goToNewsDetail = (newsId) => {
    console.log('跳转到新闻详情:', newsId)
    router.push(`/news/${newsId}`)
}

// 上一页
const prevPage = () => {
    if (currentPage.value > 1) {
        currentPage.value--
        console.log('上一页:', currentPage.value)
    }
}

// 下一页
const nextPage = () => {
    if (currentPage.value < totalPages.value) {
        currentPage.value++
        console.log('下一页:', currentPage.value)
    }
}
</script>

<style lang="less" scoped>
.news-page {
    min-height: 100vh;
    background: #fff;

    // 顶部banner区域
    .news-banner {
        height: 400px;
        background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('https://picsum.photos/1920/400?random=10');
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .banner-content {
            text-align: center;
            color: #fff;

            .banner-title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 48px;
                color: #FFFFFF;
                margin: 0 0 16px 0;
            }

            .banner-subtitle {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 300;
                font-size: 72px;
                color: rgba(255, 255, 255, 0.3);
                margin: 0;
                letter-spacing: 8px;
            }
        }
    }

    // 新闻内容区域
    .news-content {
        padding: 80px 160px 120px;

        .news-container {
            max-width: 1200px;
            margin: 0 auto;

            .news-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 43px;
                margin-bottom: 80px;

                .news-item {
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-4px);
                    }

                    .news-image {
                        width: 100%;
                        height: 280px;
                        overflow: hidden;
                        border-radius: 8px;
                        margin-bottom: 24px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            transition: transform 0.3s ease;
                        }

                        &:hover img {
                            transform: scale(1.05);
                        }
                    }

                    .news-info {
                        .news-title {
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 500;
                            font-size: 24px;
                            color: #1A1A1A;
                            margin-bottom: 16px;
                            line-height: 1.4;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                        }

                        .news-description {
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            font-size: 16px;
                            color: #666666;
                            line-height: 1.5;
                            display: -webkit-box;
                            -webkit-line-clamp: 3;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            margin-bottom: 24px;
                        }

                        .news-meta {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .news-date {
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 400;
                                font-size: 16px;
                                color: #999999;
                            }

                            .news-author {
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 400;
                                font-size: 14px;
                                color: #CCCCCC;
                            }
                        }
                    }
                }
            }

            // 分页控制
            .pagination {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 40px;

                .pagination-btn {
                    width: 48px;
                    height: 48px;
                    border: none;
                    background: #f8f9fa;
                    border-radius: 50%;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;

                    &:hover:not(.disabled) {
                        background: #e9ecef;
                        transform: scale(1.1);
                    }

                    &.disabled {
                        opacity: 0.3;
                        cursor: not-allowed;
                    }

                    img {
                        width: 16px;
                        height: 16px;
                    }
                }

                .pagination-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-size: 18px;

                    .current-page {
                        font-weight: 600;
                        color: #1A1A1A;
                    }

                    .separator {
                        color: #CCCCCC;
                    }

                    .total-pages {
                        color: #999999;
                    }
                }
            }
        }
    }
}
</style>
