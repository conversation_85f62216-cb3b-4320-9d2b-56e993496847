import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import px2rem from 'postcss-px2rem';

export default defineConfig({
    base: "./",
    plugins: [vue()],
    resolve: {
        alias: {
            "@": resolve(__dirname, "src"),
        },
        extensions: [".js", ".json", ".ts"],
    },
    server: {
        host: "0.0.0.0",
        port: 3000,
        open: true,
        proxy: {
            "/api/": {
                target: "https://ds.mufengweilai.com/",
                // target: "/api/",
                changeOrigin: true,
                // rewrite: (path) => path.replace(/^\/api/, ""),
            },
        },
    },
    css: {
        // 预处理器配置
        preprocessorOptions: {
            less: {
                javascriptEnabled: true,
            },
        },
        // PostCSS配置
        postcss: {
            plugins: [
                px2rem({
                    remUnit: 16, // 适合大屏开发的基准值
                    exclude: /node_modules|tailwind|index\.css/i,
                }),
            ],
        },
    },
});
