import { createApp } from "vue";
import App from "./App.vue";
import { router } from "./router/index";
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import "./assets/style/commonStyle.scss";
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 引入大屏适配代码
import "./utils/rem";

const app = createApp(App);

app.config.errorHandler = (err, vm, info) => {
    console.error("全局错误:", err);
    console.error("错误信息:", info);
};
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}


// 注册路由
app.use(router);
app.use(ElementPlus,{ locale: zhCn,})
// 挂载应用
app.mount("#app");
