/**
 * 滚动管理器 - 统一处理页面滚动行为
 */

/**
 * 滚动到页面顶部
 * @param {boolean} smooth - 是否使用平滑滚动
 */
export function scrollToTop(smooth = false) {
    const behavior = smooth ? 'smooth' : 'auto'
    
    // 方法1: 使用 window.scrollTo
    window.scrollTo({
        top: 0,
        left: 0,
        behavior: behavior
    })
    
    // 方法2: 直接设置 scrollTop (兼容性更好)
    document.documentElement.scrollTop = 0
    document.body.scrollTop = 0
    
    // 方法3: 处理可能的自定义滚动容器
    const containers = [
        '.main-content',
        '.layout-container',
        '.app-container'
    ]
    
    containers.forEach(selector => {
        const element = document.querySelector(selector)
        if (element) {
            element.scrollTop = 0
        }
    })
}

/**
 * 强制滚动到顶部 (多种方式确保成功)
 */
export function forceScrollToTop() {
    // 立即执行
    scrollToTop(false)
    
    // 延迟执行确保DOM更新完成
    setTimeout(() => {
        scrollToTop(false)
    }, 0)
    
    // 再次延迟执行确保路由切换完成
    setTimeout(() => {
        scrollToTop(false)
    }, 50)
}

/**
 * 路由切换时的滚动处理
 */
export function handleRouteScroll() {
    return new Promise((resolve) => {
        // 使用 requestAnimationFrame 确保在下一帧执行
        requestAnimationFrame(() => {
            forceScrollToTop()
            resolve({ top: 0, left: 0 })
        })
    })
}
